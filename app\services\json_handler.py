import json
import os

class JsonHandler:
    def __init__(self, file_path, id):
        self.file_path = file_path
        self.id = str(id)
        if not os.path.exists(file_path):
            with open(file_path, "w") as json_file:
                json.dump([], json_file)
        if not self.get_data_by_id():
            self.add_new_id()

    def read_json(self):
        with open(self.file_path, "r") as json_file:
            data = json.load(json_file)
        return data

    def write_json(self, data):
        with open(self.file_path, "w") as json_file:
            json.dump(data, json_file, indent=4)

    def add_new_id(self):
        data = self.read_json()
        data.append({"id": self.id, "messages": [], "context": ""})
        self.write_json(data)

    def update_message(self, input, output):
        data = self.read_json()
        for item in data:
            if item["id"] == self.id:
                item["messages"].append({"input": input, "output": output})
        self.write_json(data)

    def update_context(self, context):
        data = self.read_json()
        for item in data:
            if item["id"] == self.id:
                item["context"] = context
        self.write_json(data)

    def get_data_by_id(self):
        data = self.read_json()
        for item in data:
            if item["id"] == self.id:
                return item
        return None

    def delete_data_by_id(self):
        data = self.read_json()
        data = [item for item in data if item["id"] != self.id]
        self.write_json(data)
