{"$schema": "http://json-schema.org/draft-07/schema#", "title": "UploadedFileContent", "description": "Schema for uploaded file content including patient info, physician info, medical facility details, and lab reports", "type": "object", "required": ["is_lab_report", "lab_reports"], "properties": {"patient_info": {"type": "object", "title": "UploadedPatientInfo", "description": "Details of the patient details in the uploaded lab report", "required": ["first_name", "last_name"], "properties": {"first_name": {"type": "string", "nullable": true, "description": "The first name of the patient, extracted directly from the uploaded lab test report"}, "last_name": {"type": "string", "nullable": true, "description": "The last name of the patient, extracted directly from the uploaded lab test report"}}}, "physician_info": {"type": "object", "title": "UploadedPhysicianInfo", "description": "Details of the referring or treating physician", "required": ["first_name", "last_name"], "properties": {"first_name": {"type": "string", "nullable": true, "description": "The first name of the referring or treating physician, extracted from the uploaded lab test report"}, "last_name": {"type": "string", "nullable": true, "description": "The last name of the referring or treating physician, extracted from the uploaded lab test report"}}}, "medical_facility": {"type": "object", "title": "UploadedMedicalFacility", "description": "Details of the medical facility where the tests were conducted", "required": ["facility_name"], "properties": {"facility_name": {"type": "string", "nullable": true, "description": "The name of the medical facility where the tests were conducted, extracted from the lab test report"}, "location": {"type": "string", "nullable": true, "description": "The geographical location of the medical facility, if available, extracted and matched with the Institutes index"}}}, "is_lab_report": {"type": "boolean", "description": "Determine if the document is a lab report by carefully analyzing key medical indicators like test results, reference ranges, patient vitals, lab facility details, physician information, and medical terminology. The document should contain structured clinical/laboratory data before being classified as a lab report"}, "test_date": {"type": "string", "nullable": true, "format": "date-time", "description": "The date when the lab test was conducted or reported"}, "lab_reports": {"type": "array", "description": "A comprehensive list of all laboratory test results and parameters from the report", "items": {"type": "object", "title": "LabTestParameter", "required": ["name", "result", "result_value_type"], "properties": {"name": {"type": "string", "description": "The parameter name exactly as displayed in the lab report. Extract the full name including any prefixes, suffixes, or codes.\nExamples in English: 'Glucose', 'WBC', 'HDL Cholesterol', 'AST (SGOT)', 'TSH (Thyroid Stimulating Hormone)', 'BUN (Blood Urea Nitrogen)', 'MCHC', 'Vitamin D, 25-OH', 'Anti-Thyroid Peroxidase Ab', 'T. gondii IgG'.\nExamples in Hebrew: 'גלוקוז', 'ספירת תאים לבנים', 'כולסטרול HDL', 'אלנין אמינוטרנספראז (ALT)', 'הורמון מעורר בלוטת התריס (TSH)', 'אוריאה בדם (BUN)', 'אלבומין', 'תאי דם אדומים (RBC)', 'המוגלובין'.\nFor bilingual entries, preserve exactly as shown: 'ALT/אלנין אמינוטרנספראז', 'כולסטרול HDL/HDL Cholesterol'.\nFor special characters or formatting, maintain as presented: 'β-hCG', 'α-Fetoprotein', 'גאמא GT', 'Vitamin B₁₂'.\nField naming variations in English: 'Test', 'Parameter', 'Analyte', 'Component', 'Measurement', 'Test Name', 'Exam', 'Determination', 'Assay'.\nField naming variations in Hebrew: 'פרמטר', 'בדיקה', 'מדד', 'רכיב', 'אנליט', 'שם הבדיקה', 'מרכיב', 'קטלוג', 'פריט'."}, "result": {"type": "string", "nullable": true, "description": "The patient's actual test measurement or finding.\n- Extract only the patient's specific result value.\n- Do not include any reference values or normal ranges here.\n- Look for the value that appears before units or as the primary finding.\n- Example: In '70 mg/dl', extract '70' as the result.\n- Hebrew examples: '5.2', 'חיובי', 'תקין', 'לא נמצא'"}, "range": {"type": "string", "nullable": true, "description": "The expected normal or reference values for comparison.\n- Look for values labeled as \"range\", \"normal\", \"reference\", or appearing in comments.\n- Include single values that appear in comments with matching units.\n- Extract complete ranges like '3.5-5.2' or '10-100'.\n- For a single reference value like '283 mg/dl' in comments, extract '283'.\n- Hebrew examples: '0.5-5.0', 'עד 200', 'גברים: 13.5-17.5, נשים: 12.0-15.5'\n- If no reference range is provided, set to null."}, "units": {"type": "string", "nullable": true, "description": "The unit of measurement for the test result and reference ranges. Extract exactly as shown, including special characters and formatting.\nExamples include:\n    - Basic units: 'mg/dL', 'g/dL', 'μg/dL', 'ng/mL', 'pg/mL', 'mEq/L', 'mmol/L', 'μmol/L', 'U/L', 'IU/L', 'mIU/L', '%', 'g/L'.\n    - Cell count units: 'x10^9/L', 'x10^12/L', 'x10³/μL', 'x10⁶/μL', '/μL', '/hpf', '/lpf', '/mm³'.\n    - Time-based units: 'sec', 'min', 'hr', 'mL/min', 'mL/min/1.73m²'.\n    - Ratio units: 'ratio', 'index', 'AU', 'S/CO', 'titer'.\n    - Hebrew units: 'מ״ג/ד״ל', 'גרם/ד״ל', 'יחידות/ליטר', 'מ״מול/ליטר', 'אחוזים', 'שניות'.\n    - Combined units: 'cells/mm³', 'mmHg', 'gr/dL', 'fL', 'pg/cell'.\nFor qualitative results where no unit is applicable (like 'Positive'/'Negative'), set to null.\nIf units appear with ranges ('3.5-5.0 mg/dL'), extract only the unit portion ('mg/dL').\nField naming variations in English: 'Units', 'Unit', 'Measurement Units', 'Unit of Measure', 'UOM', 'Reported In', 'Scale', 'Metrics', 'Units of Measurement'.\nField naming variations in Hebrew: 'יחידות', 'יחידת מידה', 'מידה', 'סקלה', 'יחידות מדידה', 'נמדד ב', 'יח׳', 'יח׳ מדידה', 'מטריקה'."}, "test_type": {"type": "string", "nullable": true, "description": "The category or group of laboratory tests that the parameter belongs to. Usually appears as a header or section title.\nExamples include:\n    -Examples in English: 'Complete Blood Count (CBC)', 'Comprehensive Metabolic Panel (CMP)', 'Lipid Profile', 'Thyroid Function Tests', 'Liver Function Tests', 'Urinalysis', 'Hormone Panel', 'Allergy Panel', 'Infectious Disease Serology', 'Vitamin Panel', 'Electrolytes', 'Iron Studies', 'Coagulation Studies'.\n    -Examples in Hebrew: 'ספירת דם מלאה', 'פאנל מטבולי מקיף', 'פרופיל שומנים', 'בדיקות תפקודי בלוטת התריס', 'תפקוד<PERSON> כבד', 'בדיקת שתן', 'פאנל הורמונלי', 'בדיקות אלרגיה', 'סרולוגיה למחלות זיהומיות', 'בדיקות ויטמינים', 'אלקטרוליטים', 'בדיקות ברזל', 'בדיק<PERSON><PERSON> קרישה'.\n    -Bilingual examples: 'ספיר<PERSON> דם מלאה (CBC)', 'בדיקו<PERSON> תפקודי כבד (Liver Function Tests)', 'פרופיל שומנים (Lipid Profile)'.\nIf test type is not explicitly stated, attempt to infer from context, parameter grouping, or report structure.\nFor standalone tests, use a general category: 'Specialized Tests', 'בדיקות מיוחדות'. \"\nField naming variations in English: 'Test Type', 'Panel', 'Profile', 'Test Category', 'Battery', 'Test Group', 'Panel Type', 'Test Classification', 'Laboratory Section', 'Department'. \"\nField naming variations in Hebrew: 'סוג בדיקה', 'פאנל', 'פרופיל', 'קטגוריית בדיקה', 'סוללת בדיקות', 'קבוצת בדיקות', 'סיווג בדיקה', 'מחלקה מעבדתית', 'מדור'."}, "comment": {"type": "string", "nullable": true, "description": "Any additional notes, interpretations, or recommendations related to the test result.\nExtract the complete text as shown.\nExamples include:\n    - Clinical interpretation examples: 'Consistent with iron deficiency anemia', 'Suggestive of viral infection', 'Compatible with hypothyroidism', 'May indicate dehydration', 'Consider renal function assessment'.\n    - Sample quality issues: 'Hemolyzed sample', 'Lipemic specimen', 'Sample insufficient for complete analysis', 'Results may be affected by medication', 'Recommend recollection'.\n    - Follow-up recommendations: 'Recommend follow-up in 3 months', 'Consider endocrinology referral', 'Repeat test after fasting', 'Correlate with clinical symptoms'.\n    - Technical notes: 'Verified by repeat analysis', 'Confirmed by alternate method', 'Manual differential performed', 'Second sample requested'.\n    - Hebrew examples: 'מתאים לאנמיה מחוסר ברזל', 'מרמז על זיהום ויראלי', 'ממצאים תואמים תת-פעילות בלוטת התריס', 'מומלץ מעקב תוך 3 חודשים', 'דגימה המוליטית', 'יש לשקול הפניה לאנדוקרינולוג', 'מומלץ לחזור על הבדיקה בצום'.\n    - Alerts: 'Critical value called to Dr. <PERSON> on 03/11/2025', 'Results require immediate clinical attention', 'ערך קריטי, דווח לד״ר כהן ב-11/03/2025', 'תוצאות מחייבות התייחסות קלינית מיידית'.\nComments may appear in footnotes, special sections, or directly adjacent to results. Include all relevant comments for the specific parameter.\nField naming variations in English: 'Comment', 'Notes', 'Remarks', 'Interpretation', 'Clinical Notes', 'Additional Information', 'Observation', 'Comment/Interpretation', 'Clinical Significance', 'Special Notes'.\nField naming variations in Hebrew: 'הערה', 'הערות', 'פירוש', 'פרשנות', 'הערות קליניות', 'מידע נוסף', 'תצפית', 'משמעות קלינית', 'הערות מיוחדות', 'הסבר'."}, "comment_english": {"type": "string", "nullable": true, "description": "English translation of the comment field when the original comment is in Hebrew.\nThis field should only be populated when the comment field contains Hebrew text.\nThe translation should maintain medical terminology accuracy and context.\nIf the original comment is already in English, this field should remain empty (null).\nExamples:\n    - Hebrew comment: 'מתאים לאנמיה מחוסר ברזל' → comment_english: 'Consistent with iron deficiency anemia'\n    - Hebrew comment: 'מרמז על זיהום ויראלי' → comment_english: 'Suggestive of viral infection'\n    - Hebrew comment: 'מומלץ מעקב תוך 3 חודשים' → comment_english: 'Recommend follow-up in 3 months'\n    - Hebrew comment: 'יש לשקול הפניה לאנדוקרינולוג' → comment_english: 'Consider endocrinology referral'\n    - Hebrew comment: 'ערך קריטי, דווח לד״ר כהן ב-11/03/2025' → comment_english: 'Critical value, reported to <PERSON><PERSON> <PERSON> on 11/03/2025'"}, "index": {"type": "integer", "nullable": true, "description": "The index of the lab test parameter."}, "result_value_type": {"type": "string", "enum": ["numeric_value", "negative_positive", "operator_value", "blank"], "description": "The value type of the lab test result from test_params.result based on the categories defined in ParameterValueTypeInfo.\nEach parameter result must be classified into a single **value type** from the following categories:\n    1. Numeric value = The result is a number (including negatives and fractions like 0.5). Return as **numeric_value** only.\n    2. Negative / Positive = The result is a positive or negative. An example is \"Negative\", \"Positive\", \"Pos\", \"Neg\". Return as **positive_negative** only.\n    3. Operator value = The result is a number (including negatives and fractions) combined with an operator, or a number with a compound expression (e.g., <, >, +, >=, <=). An example is <2, +50, less than 3, greater than 4, etc. Return as **operator_value** only.\n    4. Blank = The result is textual and does not fit any of the above categories (e.g., \"Normal\", \"Undeteramable\", \"See attached report\", \"\", null). Return as **blank** only."}}}}}}