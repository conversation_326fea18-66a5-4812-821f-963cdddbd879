global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - prometheus-rules.yml

alerting:
  alertmanagers:
    - static_configs:
        - targets:
            - alertmanager:9093

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['prometheus:9090']

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']

  - job_name: 'nginx-exporter'
    static_configs:
      - targets: ['nginx-exporter:9113']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']


  # - job_name: 'nginx'
  #   static_configs:
  #     - targets: ['nginx:80']
 
  # - job_name: 'app'
  #   static_configs:
  #     - targets: ['app:8000']