from slack_sdk import Web<PERSON>lient
from slack_sdk.errors import SlackApiError
from app.core.config import settings
from app.util.logger import logger

def send_slack_notification(message: str) -> None:
    """
    Send a notification message to Slack.
    
    Args:
        message (str): The message to send to <PERSON>lack
    """
    # Initialize a WebClient instance with your OAuth token
    slack_client = WebClient(token=settings.SLACK_TOKEN)

    # The channel ID or name where you want to send the message
    channel_id = "api-key-alert"

    # The bot name
    bot_name = "openai-api-key-alert"
    
    try:
        # Use the chat.postMessage method to send a message to the channel
        response = slack_client.chat_postMessage(
            channel=channel_id,
            text=message,
            username=bot_name
        )
        logger.info("Message sent successfully to <PERSON>lack!")
    except SlackApiError as e:
        # Error handling in case the message fails to send
        logger.error(f"Error sending message to Slack: {e}")
