import requests
from app.core.config import settings
from app.util.slack_notifier import send_slack_notification


class LlamaIndexAPI:
    """
    A class to interact with the LlamaIndex API.
    """

    BASE_URL = "https://api.cloud.llamaindex.ai/api/parsing"

    def __init__(self, api_key: str = settings.LLAMA_CLOUD_API_KEY):
        """
        Initialize the API client with the API key.

        Args:
            api_key (str): Your LlamaIndex API key.
        """
        self.api_key = api_key
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "accept": "application/json",
        }

    def upload_file(
        self,
        file_path: str,
        parse_mode="parse_page_with_lvm",
        vendor_multimodal_model_name="anthropic-sonnet-3.7",
    ) -> dict:
        """
        Upload a file for parsing with specified model and parse mode.

        Args:
            file_path (str): Path to the file to be uploaded.
            parse_mode (str): The mode to use for parsing.
            vendor_multimodal_model_name (str): The vendor multimodal model name.

        Returns:
            dict: The response from the API.
        """
        url = f"{self.BASE_URL}/upload"

        files = {
            "file": (
                file_path.name,
                open(file_path, "rb"),
                "application/pdf",
            ),
        }

        data = {
            "parse_mode": parse_mode,
            "vendor_multimodal_model_name": vendor_multimodal_model_name,
            "input_url": "",
            "structured_output": False,
            "disable_ocr": False,
            "disable_image_extraction": False,
            "adaptive_long_table": False,
            "annotate_links": False,
            "do_not_unroll_columns": False,
            "html_make_all_elements_visible": False,
            "html_remove_navigation_elements": False,
            "html_remove_fixed_elements": False,
            "guess_xlsx_sheet_name": False,
            "do_not_cache": False,
            "invalidate_cache": False,
            "output_pdf_of_document": False,
            "take_screenshot": False,
            "is_formatting_instruction": True,
        }

        try:
            response = requests.post(url, headers=self.headers, files=files, data=data)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            error_msg = f"🔴 LlamaParse Upload Error: {str(e)} for file {file_path}"
            send_slack_notification(error_msg)
            print(f"An error occurred during file upload: {e}")
            return {"error": str(e)}
        finally:
            files["file"][1].close()

    def get_job_status(self, job_id: str) -> dict:
        """
        Get the status of a parsing job.

        Args:
            job_id (str): The job ID to query.

        Returns:
            dict: The response from the API.
        """
        url = f"{self.BASE_URL}/job/{job_id}"
        try:
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            error_msg = f"🔴 LlamaParse Status Check Error: {str(e)} for job {job_id}"
            send_slack_notification(error_msg)
            print(f"An error occurred while fetching job status: {e}")
            return {"error": str(e)}

    def get_job_result_markdown(self, job_id: str) -> dict:
        """
        Get the markdown result of a parsing job.

        Args:
            job_id (str): The job ID for the parsing result.

        Returns:
            dict: The response from the API.
        """
        url = f"{self.BASE_URL}/job/{job_id}/result/markdown"
        try:
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            error_msg = f"🔴 LlamaParse Result Error: {str(e)} for job {job_id}"
            send_slack_notification(error_msg)
            print(f"An error occurred while fetching job result: {e}")
            return {"error": str(e)}
