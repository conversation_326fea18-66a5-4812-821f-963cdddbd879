{"user_id": "288", "document_summary": {"progress": 100, "status": "complete", "data": {"is_lab_report": true, "patient_info": {"first_name": "מיתר", "last_name": "שמואלי"}, "physician_info": {"first_name": "בו<PERSON><PERSON>", "last_name": "גבע"}, "medical_facility": {"facility_name": null, "location": null}, "test_date": "2012-01-04T00:00:00", "lab_reports": [{"name": "Glucose (B)", "result": 103, "range": "70-100", "units": "mg/dl", "test_type": "כימיה בדם", "comment": null, "comment_english": null, "index": 0, "result_value_type": "numeric_value"}, {"name": "Urea (B)", "result": 30, "range": "17-49", "units": "mg/dl", "test_type": "כימיה בדם", "comment": null, "comment_english": null, "index": 1, "result_value_type": "numeric_value"}, {"name": "K+ Potassium (B)", "result": 4.2, "range": "3.5-5.1", "units": "mmol/l", "test_type": "כימיה בדם", "comment": null, "comment_english": null, "index": 2, "result_value_type": "numeric_value"}, {"name": "Na- Sodium (B)", "result": 137, "range": "136-146", "units": "mmol/l", "test_type": "כימיה בדם", "comment": null, "comment_english": null, "index": 3, "result_value_type": "numeric_value"}, {"name": "Protein Total (B)", "result": 6.9, "range": "6.6-8.3", "units": "g/dl", "test_type": "כימיה בדם", "comment": null, "comment_english": null, "index": 4, "result_value_type": "numeric_value"}, {"name": "Albumin (B)", "result": 4.3, "range": "3.5-5.2", "units": "g/dl", "test_type": "כימיה בדם", "comment": null, "comment_english": null, "index": 5, "result_value_type": "numeric_value"}, {"name": "ALT (GPT)", "result": 39.4, "range": "0-37", "units": "U/l", "test_type": "כימיה בדם", "comment": null, "comment_english": null, "index": 6, "result_value_type": "numeric_value"}, {"name": "AST (GOT)", "result": 25, "range": "10-37", "units": "U/l", "test_type": "כימיה בדם", "comment": null, "comment_english": null, "index": 7, "result_value_type": "numeric_value"}, {"name": "CK", "result": 70, "range": "0-170", "units": "u/l", "test_type": "כימיה בדם", "comment": null, "comment_english": null, "index": 8, "result_value_type": "numeric_value"}, {"name": "ESR 1 hour", "result": 20, "range": "0-20", "units": "mm/hr", "test_type": "המטולוגיה-כללי", "comment": null, "comment_english": null, "index": 9, "result_value_type": "numeric_value"}, {"name": "WBC-Leucocytes", "result": 10.1, "range": "4.5-11", "units": "10^3/micl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 10, "result_value_type": "numeric_value"}, {"name": "RBC-Red Blood Cells", "result": 4.69, "range": "4.5-5.5", "units": "10^6/micl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 11, "result_value_type": "numeric_value"}, {"name": "Hemoglobin", "result": 14.2, "range": "13.5-17.5", "units": "g/dl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 12, "result_value_type": "numeric_value"}, {"name": "Hematocrit", "result": 41.7, "range": "41-53", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 13, "result_value_type": "numeric_value"}, {"name": "MCV-Mean Cell Volume", "result": 88.9, "range": "79-97", "units": "fl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 14, "result_value_type": "numeric_value"}, {"name": "MCH-Mean Cell Hemoglobin", "result": 30.3, "range": "27-34", "units": "pg/cell", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 15, "result_value_type": "numeric_value"}, {"name": "MCHC-M.Cell Hb cont.", "result": 34.1, "range": "32-36", "units": "g/dl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 16, "result_value_type": "numeric_value"}, {"name": "RDW-Red Cell Distri.Width", "result": 13, "range": "11.6-15", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 17, "result_value_type": "numeric_value"}, {"name": "Platelets", "result": 185, "range": "150-450", "units": "10^3/micl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 18, "result_value_type": "numeric_value"}, {"name": "MPV-Mean Platelet Volume", "result": 11.3, "range": "8.5-12.9", "units": "fl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": "החל מ-4.11.08 יש לב לשיפור ערכי הייחוס.", "comment_english": "As of 4.11.08, attention to improvement in reference values.", "index": 19, "result_value_type": "numeric_value"}, {"name": "Neutrophils %", "result": 61.9, "range": "40-75", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 20, "result_value_type": "numeric_value"}, {"name": "Lymphocytes %", "result": 25, "range": "22-44", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 21, "result_value_type": "numeric_value"}, {"name": "Monocytes %", "result": 12.1, "range": "3-13", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 22, "result_value_type": "numeric_value"}, {"name": "Eosinophils %", "result": 0.7, "range": "0-6", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 23, "result_value_type": "numeric_value"}, {"name": "Basophils %", "result": 0.3, "range": "0-2", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 24, "result_value_type": "numeric_value"}, {"name": "Neutrophils #", "result": 6.23, "range": "1.8-7.7", "units": "#", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 25, "result_value_type": "numeric_value"}, {"name": "Lymphocytes #", "result": 2.52, "range": "1-4.8", "units": "#", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 26, "result_value_type": "numeric_value"}, {"name": "Monocytes #", "result": 1.22, "range": "0-1.1", "units": "#", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 27, "result_value_type": "numeric_value"}, {"name": "Eosinophils #", "result": 0.07, "range": "0-0.6", "units": "#", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 28, "result_value_type": "numeric_value"}, {"name": "Basophils #", "result": 0.03, "range": "0-0.2", "units": "#", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 29, "result_value_type": "numeric_value"}]}}, "matched_physician": {"progress": 100, "status": "complete", "data": {"matched_id": 1414, "matched_title": "Dr.", "matched_name": "Boaz", "matched_lastname": "<PERSON><PERSON>", "match_info": {"match_score": "Exact", "reason": "The physician name '<PERSON><PERSON>' matches exactly with the provided name 'בוע<PERSON> גבע'."}}}, "matched_institution": {"progress": 100, "status": "complete", "data": {"value_name": null, "matched_display_name": null, "matched_id": null, "match_info": {"match_score": "Unknown", "reason": "Facility data is not found in uploaded document"}}}, "lab_reports": {"progress": 100, "status": "complete", "data": [{"match_data": {"id": 128, "test_type": "Complete Blood Count (CBC)", "parameter": "Neutrophils % (NEUT%)", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter 'Neutrophils %' matches conceptually with the parameter 'Neutrophils % (NEUT%)' with minor differences in formatting."}, "match_test_type_info": {"match_score": "Unknown", "reason": "The reported test type 'המטולוגיה- ספירת דם - מכ<PERSON><PERSON><PERSON>' does not match any known test types in the index."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 61.9, "unit": "%"}, "conversion_details": null, "comment": "The result unit '%' is the same as the reference unit. No conversion required."}, "test_params": {"name": "Neutrophils %", "result": 61.9, "range": "40-75", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 20, "result_value_type": "numeric_value"}}, {"match_data": {"id": 397, "test_type": "Metabolic Panel", "parameter": "Glucose (Glu) - blood", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The lab report parameter 'Glucose (B)' matches conceptually with 'Glucose (Glu) - blood' but has a slight difference in spelling (B vs Glu)."}, "match_test_type_info": {"match_score": "Unknown", "reason": "The test type in the lab report 'כימיה בדם' has no equivalent match in the index."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 103, "unit": "mg/dL"}, "conversion_details": null, "comment": "No conversion needed as the result is already in the reference unit (mg/dL)."}, "test_params": {"name": "Glucose (B)", "result": 103, "range": "70-100", "units": "mg/dl", "test_type": "כימיה בדם", "comment": null, "comment_english": null, "index": 0, "result_value_type": "numeric_value"}}, {"match_data": {"id": 463, "test_type": "Metabolic Panel", "parameter": "Urea nitrogen (BUN) - blood", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter 'Urea (B)' in the lab report matches closely with 'Urea nitrogen (BUN) - blood', considering minor formatting differences."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The test type 'כימיה בדם' translates to 'Metabolic Panel', which is the matched test type in the context."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 30, "unit": "mg/dL"}, "conversion_details": {"factor": null, "calculation": null}, "comment": "The result is in the same unit as the reference unit, so no conversion was necessary."}, "test_params": {"name": "Urea (B)", "result": 30, "range": "17-49", "units": "mg/dl", "test_type": "כימיה בדם", "comment": null, "comment_english": null, "index": 1, "result_value_type": "numeric_value"}}, {"match_data": {"id": 337, "test_type": "Metabolic Panel", "parameter": "Alanine amino transferase, ALT (GPT) - blood", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "Parameter name matches closely despite minor formatting differences."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "Test type matches closely even with different case formatting."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 39.4, "unit": "U/L"}, "conversion_details": {"factor": null, "calculation": null}, "comment": "The result is already in the reference unit."}, "test_params": {"name": "ALT (GPT)", "result": 39.4, "range": "0-37", "units": "U/l", "test_type": "כימיה בדם", "comment": null, "comment_english": null, "index": 6, "result_value_type": "numeric_value"}}, {"match_data": {"id": 113, "test_type": "Complete Blood Count (CBC)", "parameter": "Basophils % (BASO%)", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter name 'Basophils %' matches with 'Basophils % (BASO%)' in the database with slight difference in wording for test type."}, "match_test_type_info": {"match_score": "Alternative", "reason": "The test type as 'Hematology - Complete Blood Count - Device' is conceptually similar to 'Complete Blood Count (CBC)' but uses different phrasing."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 0.3, "unit": "%"}, "conversion_details": null, "comment": "No conversion needed as the unit is the same as the reference unit."}, "test_params": {"name": "Basophils %", "result": 0.3, "range": "0-2", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 24, "result_value_type": "numeric_value"}}, {"match_data": {"id": 119, "test_type": "Complete Blood Count (CBC)", "parameter": "Lymphocytes % (LYM%)", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter name matches exactly in the database, with only minor formatting differences."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The test type in the lab report closely resembles the test type in the database with minor variations."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 25, "unit": "%"}, "conversion_details": null, "comment": "The result is already in the reference unit."}, "test_params": {"name": "Lymphocytes %", "result": 25, "range": "22-44", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 21, "result_value_type": "numeric_value"}}, {"match_data": {"id": 352, "test_type": "Metabolic Panel", "parameter": "Aspratate aminotransferase, AST (GOT) - blood", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter name 'AST (GOT)' is conceptually the same as 'Aspratate aminotransferase, AST (GOT) - blood' with differences in formatting."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The test type 'כימיה בדם' translates to 'Metabolic Panel', which is similar with slight language differences."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 25, "unit": "U/L"}, "conversion_details": {"factor": null, "calculation": null}, "comment": "The result is already in the reference unit."}, "test_params": {"name": "AST (GOT)", "result": 25, "range": "10-37", "units": "U/l", "test_type": "כימיה בדם", "comment": null, "comment_english": null, "index": 7, "result_value_type": "numeric_value"}}, {"match_data": {"id": 115, "test_type": "Complete Blood Count (CBC)", "parameter": "Eosinophils % (EOS%)", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter name 'Eosinophils %' from the lab report closely matches 'Eosinophils % (EOS%)' in the document, with minor differences in formatting."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The test type 'המטולוגיה- ספירת דם - מכ<PERSON><PERSON><PERSON>' translates to 'Hematology - Complete Blood Count - Device', which is equivalent to 'Complete Blood Count (CBC)' in the document."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 0.7, "unit": "%"}, "conversion_details": null, "comment": "The result is in the same unit as the reference."}, "test_params": {"name": "Eosinophils %", "result": 0.7, "range": "0-6", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 23, "result_value_type": "numeric_value"}}, {"match_data": {"id": 692, "test_type": "Metabolic panel", "parameter": "ESR", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter name 'ESR' matches exactly, minor formatting with unit differences."}, "match_test_type_info": {"match_score": "Unknown", "reason": "The test type 'Hematology-General' does not match the document index."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 20, "unit": "mm/hr"}, "conversion_details": null, "comment": "No conversion needed as the units are equivalent."}, "test_params": {"name": "ESR 1 hour", "result": 20, "range": "0-20", "units": "mm/hr", "test_type": "המטולוגיה-כללי", "comment": null, "comment_english": null, "index": 9, "result_value_type": "numeric_value"}}, {"match_data": {"id": 121, "test_type": "Complete Blood Count (CBC)", "parameter": "MCH", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "Parameter 'MCH-Mean Cell Hemoglobin' matches 'MCH' with slight format differences."}, "match_test_type_info": {"match_score": "Alternative", "reason": "Test type 'המטולוגיה- ספירת דם - מכש<PERSON><PERSON>' is an alternative name for 'Complete Blood Count (CBC)'."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 30.3, "unit": "pg"}, "conversion_details": null, "comment": "No conversion needed, provided unit is compatible with the reference unit."}, "test_params": {"name": "MCH-Mean Cell Hemoglobin", "result": 30.3, "range": "27-34", "units": "pg/cell", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 15, "result_value_type": "numeric_value"}}, {"match_data": {"id": 129, "test_type": "Complete Blood Count (CBC)", "parameter": "Neutrophils No. (NEUT#)", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter 'Neutrophils #' matches 'Neutrophils No. (NEUT#)' with a minor formatting difference in units."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The test type 'hematology- blood count' is conceptually similar to 'Complete Blood Count (CBC)'."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 6.23, "unit": "10e3/microL"}, "conversion_details": {"factor": 1, "calculation": "result remains the same as it was already in the conversion unit."}, "comment": "The result was in units '#' which is interpreted as equivalent to '10e3/microL'."}, "test_params": {"name": "Neutrophils #", "result": 6.23, "range": "1.8-7.7", "units": "#", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 25, "result_value_type": "numeric_value"}}, {"match_data": {"id": 126, "test_type": "Complete Blood Count (CBC)", "parameter": "Monocytes No. (MONO#)", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "Parameter names are conceptually similar, with a minor discrepancy in nomenclature."}, "match_test_type_info": {"match_score": "Unknown", "reason": "Test types do not match as they use different terminology."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 1.22, "unit": "10e3/microL"}, "conversion_details": {"factor": 1, "calculation": "1.22 cells * (1/1000) * (1000) = 1.22"}, "comment": "Conversion was successful from # to 10e3/microL."}, "test_params": {"name": "Monocytes #", "result": 1.22, "range": "0-1.1", "units": "#", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 27, "result_value_type": "numeric_value"}}, {"match_data": {"id": 122, "test_type": "Complete Blood Count (CBC)", "parameter": "MCHC", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter 'MCHC-M.Cell Hb cont.' aligns with 'MCHC' from the context data, allowing for variations like case differences."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The test type in the report matches closely with 'Complete Blood Count (CBC)' from the context data, accounting for a minor typographical variation."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 34.1, "unit": "g/dL"}, "conversion_details": null, "comment": "The result is already in the reference unit."}, "test_params": {"name": "MCHC-M.Cell Hb cont.", "result": 34.1, "range": "32-36", "units": "g/dl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 16, "result_value_type": "numeric_value"}}, {"match_data": {"id": 124, "test_type": "Complete Blood Count (CBC)", "parameter": "Mean Platelet Volume (MPV)", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The lab report parameter 'MPV-Mean Platelet Volume' matches the existing parameter 'Mean Platelet Volume (MPV)' with minor discrepancies in spacing and wording."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The test type 'Hematology-Complete Blood Count' is a similar expression of 'Complete Blood Count (CBC)' with slight variations."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 11.3, "unit": "fl"}, "conversion_details": null, "comment": "No conversion needed as the unit matches the reference unit."}, "test_params": {"name": "MPV-Mean Platelet Volume", "result": 11.3, "range": "8.5-12.9", "units": "fl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": "החל מ-4.11.08 יש לב לשיפור ערכי הייחוס.", "comment_english": "As of 4.11.08, attention to improvement in reference values.", "index": 19, "result_value_type": "numeric_value"}}, {"match_data": {"id": 116, "test_type": "Complete Blood Count (CBC)", "parameter": "Eosinophils No. (EOS#)", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Alternative", "reason": "Parameter name matches conceptually with alternative medical terminology."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "Test type is very similar under the same context (CBC)."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 0.07, "unit": "10e3/microL"}, "conversion_details": {"factor": null, "calculation": null}, "comment": "Converted result to standardized unit of 10e3/microL."}, "test_params": {"name": "Eosinophils #", "result": 0.07, "range": "0-0.6", "units": "#", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 28, "result_value_type": "numeric_value"}}, {"match_data": {"id": 114, "test_type": "Complete Blood Count (CBC)", "parameter": "Basophils No. (BASO#)", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter name matches conceptually with minor formatting differences."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The test type matches conceptually with minor differences in capitalization and format."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 30, "unit": "10e3/microL"}, "conversion_details": {"factor": 1000, "calculation": "0.03 # * 1000 = 30 10e3/microL"}, "comment": "Successfully converted from # to 10e3/microL."}, "test_params": {"name": "Basophils #", "result": 0.03, "range": "0-0.2", "units": "#", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 29, "result_value_type": "numeric_value"}}, {"match_data": {"id": 338, "test_type": "Metabolic Panel", "parameter": "Albumin (Alb) - blood", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter name 'Albumin (B)' is conceptually similar to '<PERSON>in (Alb)' in Document 1, but naming varies slightly."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The test type 'Chemistry' is conceptually similar to 'Metabolic Panel'."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 43, "unit": "gr/L"}, "conversion_details": {"factor": 10, "calculation": "4.3 g/dL * 10 = 43 gr/L"}, "comment": "Converted from g/dL to gr/L."}, "test_params": {"name": "Albumin (B)", "result": 4.3, "range": "3.5-5.2", "units": "g/dl", "test_type": "כימיה בדם", "comment": null, "comment_english": null, "index": 5, "result_value_type": "numeric_value"}}, {"match_data": {"id": 117, "test_type": "Complete Blood Count (CBC)", "parameter": "<PERSON><PERSON><PERSON><PERSON> (HCT)", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter 'Hematocrit' matches perfectly with 'Hematocrit (HCT)' in the database with minor variations in labeling."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The test type 'Hematology - Blood Count - Machine' is a broader classification of 'Complete Blood Count (CBC)', showing a similar category."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 41.7, "unit": "%"}, "conversion_details": null, "comment": "No conversion needed as the unit is already in the reference unit."}, "test_params": {"name": "Hematocrit", "result": 41.7, "range": "41-53", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 13, "result_value_type": "numeric_value"}}, {"match_data": {"id": 455, "test_type": "Metabolic Panel", "parameter": "Sodium (Na) - blood", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter 'Sodium (Na)' is a direct match to 'Na- Sodium (B)', with minor differences in naming."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The test type 'Metabolic Panel' closely relates to 'כימיה בדם' (Blood Chemistry), showing a conceptually similar relation."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 137, "unit": "mmol/L"}, "conversion_details": {"factor": null, "calculation": null}, "comment": "No conversion needed, result is already in the reference unit."}, "test_params": {"name": "Na- Sodium (B)", "result": 137, "range": "136-146", "units": "mmol/l", "test_type": "כימיה בדם", "comment": null, "comment_english": null, "index": 3, "result_value_type": "numeric_value"}}, {"match_data": {"id": 374, "test_type": "Metabolic Panel", "parameter": "<PERSON><PERSON><PERSON> (CK, CPK) - blood", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter name matches conceptually but has formatting differences in case."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The test type matches exactly with the documented index."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 70, "unit": "U/L"}, "conversion_details": {"factor": null, "calculation": null}, "comment": "No conversion needed as the units match."}, "test_params": {"name": "CK", "result": 70, "range": "0-170", "units": "u/l", "test_type": "כימיה בדם", "comment": null, "comment_english": null, "index": 8, "result_value_type": "numeric_value"}}, {"match_data": {"id": 118, "test_type": "Complete Blood Count (CBC)", "parameter": "Hemoglobin (Hb)", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "Exact match by parameter name and corresponding unit."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "Test type is similar with capitalization difference; it's an exact content match."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 14.2, "unit": "g/dL"}, "conversion_details": null, "comment": "The unit is already in the reference unit; no conversion needed."}, "test_params": {"name": "Hemoglobin", "result": 14.2, "range": "13.5-17.5", "units": "g/dl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 12, "result_value_type": "numeric_value"}}, {"match_data": {"id": 136, "test_type": "Complete Blood Count (CBC)", "parameter": "WBC", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter WBC-Leucocytes closely matches WBC, with minor differences in terminology."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "Test type name similarity with minor variations."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 10.1, "unit": "10e3/microL"}, "conversion_details": null, "comment": "The unit is already in the reference unit."}, "test_params": {"name": "WBC-Leucocytes", "result": 10.1, "range": "4.5-11", "units": "10^3/micl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 10, "result_value_type": "numeric_value"}}, {"match_data": {"id": 443, "test_type": "Metabolic Panel", "parameter": "Potassium (K) - blood", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter names match with minor formatting differences."}, "match_test_type_info": {"match_score": "Alternative", "reason": "The test types are conceptually similar."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 4.2, "unit": "mmol/L"}, "conversion_details": null, "comment": "The result unit matches the reference unit, no conversion needed."}, "test_params": {"name": "K+ Potassium (B)", "result": 4.2, "range": "3.5-5.1", "units": "mmol/l", "test_type": "כימיה בדם", "comment": null, "comment_english": null, "index": 2, "result_value_type": "numeric_value"}}, {"match_data": {"id": 131, "test_type": "Complete Blood Count (CBC)", "parameter": "Platelet count (PLT)", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter name 'Platelet count (PLT)' matches conceptually with 'Platelets' despite some formatting differences."}, "match_test_type_info": {"match_score": "Alternative", "reason": "The test type 'Complete Blood Count (CBC)' is conceptually related to 'Hematology - Blood Count - Device'."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 185, "unit": "10e3/microL"}, "conversion_details": {"factor": null, "calculation": null}, "comment": "The original units matched the reference unit, hence no conversion was necessary."}, "test_params": {"name": "Platelets", "result": 185, "range": "150-450", "units": "10^3/micl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 18, "result_value_type": "numeric_value"}}, {"match_data": {"id": 125, "test_type": "Complete Blood Count (CBC)", "parameter": "Monocytes % (MONO%)", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter matches exactly in name and unit with possible minor differences in formatting (e.g., case sensitivity)."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "Test type from lab report can be interpreted as Related to Complete Blood Count testing upon translation."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 12.1, "unit": "%"}, "conversion_details": null, "comment": "No conversion was needed as the unit of measurement is the same as the reference."}, "test_params": {"name": "Monocytes %", "result": 12.1, "range": "3-13", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 22, "result_value_type": "numeric_value"}}, {"match_data": {"id": 133, "test_type": "Complete Blood Count (CBC)", "parameter": "RDW", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "Exact match in the parameter name with minor differences in case."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "Exact match for the test type with case differences only."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 13, "unit": "%"}, "conversion_details": null, "comment": "No conversion needed; the units are equivalent."}, "test_params": {"name": "RDW-Red Cell Distri.Width", "result": 13, "range": "11.6-15", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 17, "result_value_type": "numeric_value"}}, {"match_data": {"id": 132, "test_type": "Complete Blood Count (CBC)", "parameter": "RBC", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "Exact match for the parameter name 'RBC'."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "Test type 'Complete Blood Count (CBC)' aligns with report's test type."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 4.69, "unit": "10e6/microL"}, "conversion_details": null, "comment": "Units are equivalent, no conversion required."}, "test_params": {"name": "RBC-Red Blood Cells", "result": 4.69, "range": "4.5-5.5", "units": "10^6/micl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 11, "result_value_type": "numeric_value"}}, {"match_data": {"id": 120, "test_type": "Complete Blood Count (CBC)", "parameter": "Lymphocytes No. (LYM#)", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter 'Lymphocytes #' is similar to 'Lymphocytes No. (LYM#)' with a minor difference in naming format."}, "match_test_type_info": {"match_score": "Alternative", "reason": "The test type 'Hematology - Blood Count - Device' is considered an alternative name for 'Complete Blood Count (CBC)'."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 2.52, "unit": "10e3/microL"}, "conversion_details": {"factor": 1, "calculation": "Conversion from cells/ul to cells/ul is a direct match."}, "comment": "The report parameter 'Lymphocytes #' can be expressed in the standardized unit as cells per microliter."}, "test_params": {"name": "Lymphocytes #", "result": 2.52, "range": "1-4.8", "units": "#", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 26, "result_value_type": "numeric_value"}}, {"match_data": {"id": 448, "test_type": "Metabolic Panel", "parameter": "Protein total (TP) - blood", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "Parameter name matches conceptually with minor naming differences."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "Test type is conceptually similar to the provided test type in context."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 69, "unit": "gr/L"}, "conversion_details": {"factor": 10, "calculation": "6.9 g/dL × 10 = 69 gr/L"}, "comment": "Conversion performed from g/dL to gr/L."}, "test_params": {"name": "Protein Total (B)", "result": 6.9, "range": "6.6-8.3", "units": "g/dl", "test_type": "כימיה בדם", "comment": null, "comment_english": null, "index": 4, "result_value_type": "numeric_value"}}, {"match_data": {"id": 536, "test_type": "Complete Blood Count (CBC)", "parameter": "MCVr", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter name MCV closely resembles the stored parameter name MCVr with minor formatting differences."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The test type in the report related closely to the Complete Blood Count, matching the analysis."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 88.9, "unit": "fL"}, "conversion_details": {"factor": null, "calculation": null}, "comment": "No conversion needed as the unit matches the reference unit."}, "test_params": {"name": "MCV-Mean Cell Volume", "result": 88.9, "range": "79-97", "units": "fl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 14, "result_value_type": "numeric_value"}}]}, "analysis": {"stage": "lab_report_matching", "message": "Successfully matched all lab reports"}}