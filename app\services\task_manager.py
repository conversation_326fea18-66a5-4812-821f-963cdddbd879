from fastapi import BackgroundTasks
from typing import List, Any, Callable, Coroutine
import asyncio
import threading
from contextlib import asynccontextmanager
from functools import partial
from app.util.logger import logger


class TaskManager:
    def __init__(self):
        self._background_tasks: List[asyncio.Task] = []
        self._threads: List[threading.Thread] = []
        self._lock = threading.Lock()
        logger.info("TaskManager initialized")

    async def add_background_task(self, task_func: Callable[..., Coroutine], *args, **kwargs) -> None:
        """Add an async task to run in the background using the event loop"""
        task = asyncio.create_task(task_func(*args, **kwargs))
        self._background_tasks.append(task)
        logger.info(f"Added new background task: {task_func.__name__}")

    def add_thread_task(self, task_func: Callable, *args, **kwargs) -> None:
        """Add a task to run in a separate thread"""
        thread = threading.Thread(target=task_func, args=args, kwargs=kwargs)
        with self._lock:
            self._threads.append(thread)
        thread.start()
        logger.info(f"Started new thread task: {task_func.__name__}")

    async def await_all_tasks(self) -> List[Any]:
        """Wait for all background tasks to complete and return results"""
        if not self._background_tasks:
            logger.info("No background tasks to await")
            return []
            
        logger.info(f"Awaiting {len(self._background_tasks)} background tasks")
        completed_tasks = await asyncio.gather(*self._background_tasks, return_exceptions=True)
        self._background_tasks.clear()
        logger.info("All background tasks completed")
        return completed_tasks

    def join_all_threads(self) -> None:
        """Wait for all thread tasks to complete"""
        with self._lock:
            threads = self._threads.copy()
            self._threads.clear()
            
        logger.info(f"Joining {len(threads)} thread tasks")
        for thread in threads:
            thread.join()
        logger.info("All thread tasks joined")

    async def cleanup(self) -> None:
        """Clean up all running tasks"""
        # Cancel any running background tasks
        logger.info("Starting cleanup of all tasks")
        for task in self._background_tasks:
            if not task.done():
                task.cancel()
                logger.info(f"Cancelled background task: {task}")
        
        # Wait for tasks to be cancelled
        if self._background_tasks:
            logger.info("Waiting for background tasks to cancel")
            await asyncio.gather(*self._background_tasks, return_exceptions=True)
        self._background_tasks.clear()

        # Stop all threads
        with self._lock:
            thread_count = len(self._threads)
            for thread in self._threads:
                if thread.is_alive():
                    thread.join(timeout=0.5)
                    logger.info(f"Joined thread: {thread}")
            self._threads.clear()
        logger.info(f"Cleaned up {thread_count} threads")


# Global task manager instance
task_manager = TaskManager()


@asynccontextmanager
async def lifespan_task_manager(app: Any):
    """Lifespan context manager for the task manager"""
    logger.info("Starting task manager lifespan")
    yield
    # Cleanup on shutdown
    logger.info("Shutting down task manager")
    await task_manager.cleanup()
