#!/usr/bin/env python3
"""
JSON Comparison Script
Compares base JSON files with input JSON files in batches of 5.
Converts input JSON to lowercase before comparison.
Only compares fields that exist in the input JSON.
"""

import json
import os
from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor
from typing import Dict, Any, List, Tuple
import logging

# Directory paths
BASE_JSONS_DIR = "base_jsons"
INPUT_JSONS_DIR = "llamaextract_results"
RESULTS_DIR = "comparison_results"

# Create results directory if it doesn't exist
os.makedirs(RESULTS_DIR, exist_ok=True)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(RESULTS_DIR, 'comparison_results.log')),
        logging.StreamHandler()
    ]
)

# File lists - specify your JSON filenames here
# The script will automatically fetch these files from their respective directories
BASE_JSON_FILENAMES = [
    # Add your base JSON filenames here (will be fetched from base_jsons/ directory)
    # Example: "sample_base_json.json"
]

INPUT_JSON_FILENAMES = [
    # Add your input JSON filenames here (will be fetched from llamaextract_results/ directory)
    # Example: "extraction_result_20150104_maccabi_20250602_173207.json"
]

# Keys to compare - based on the input JSON structure
# Input JSON structure (sample):
# {
#     "patient_info": {"first_name": "...", "last_name": "..."},
#     "physician_info": {"first_name": "...", "last_name": "..."},
#     "medical_facility": {"facility_name": "...", "location": "..."},
#     "is_lab_report": true,
#     "test_date": "...",
#     "lab_reports": [
#         {
#             "name": "...", "result": "...", "range": "...", "units": "...",
#             "test_type": "...", "comment": "...", "comment_english": "...",
#             "index": 0, "result_value_type": "..."
#         }
#     ]
# }

# Specify which top-level keys to compare (leave empty to compare all keys in input JSON)
KEYS_TO_COMPARE = [
    "patient_info",
    "physician_info",
    "medical_facility",
    "is_lab_report",
    "test_date",
    "lab_reports"
]

def get_json_files_from_directories():
    """
    Get all JSON files from base_jsons and llamaextract_results directories.
    Returns sorted lists to ensure consistent ordering.
    """
    base_files = []
    input_files = []

    # Get base JSON files
    if os.path.exists(BASE_JSONS_DIR):
        base_files = [os.path.join(BASE_JSONS_DIR, f) for f in os.listdir(BASE_JSONS_DIR)
                     if f.endswith('.json')]
        base_files.sort()  # Sort for consistent ordering

    # Get input JSON files
    if os.path.exists(INPUT_JSONS_DIR):
        input_files = [os.path.join(INPUT_JSONS_DIR, f) for f in os.listdir(INPUT_JSONS_DIR)
                      if f.endswith('.json')]
        input_files.sort()  # Sort for consistent ordering

    return base_files, input_files

def convert_to_lowercase(obj: Any) -> Any:
    """
    Recursively convert all string values in a JSON object to lowercase.
    """
    if isinstance(obj, dict):
        return {key: convert_to_lowercase(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_to_lowercase(item) for item in obj]
    elif isinstance(obj, str):
        return obj.lower()
    else:
        return obj

def extract_fields_from_input(input_data: Dict[str, Any], base_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract only the fields that exist in input_data from base_data.
    The base_data structure has the actual data nested under document_summary.data
    """
    result = {}

    # Extract the actual data from base JSON structure
    if "document_summary" in base_data and "data" in base_data["document_summary"]:
        actual_base_data = base_data["document_summary"]["data"]
    else:
        actual_base_data = base_data

    # Use specified keys or all keys from input
    keys_to_process = KEYS_TO_COMPARE if KEYS_TO_COMPARE else input_data.keys()

    for key in keys_to_process:
        if key in input_data and key in actual_base_data:
            if isinstance(input_data[key], dict) and isinstance(actual_base_data[key], dict):
                result[key] = extract_fields_from_input(input_data[key], actual_base_data[key])
            elif isinstance(input_data[key], list) and isinstance(actual_base_data[key], list):
                # For lists, extract corresponding items by index
                result[key] = []
                for i, input_item in enumerate(input_data[key]):
                    if i < len(actual_base_data[key]):
                        if isinstance(input_item, dict) and isinstance(actual_base_data[key][i], dict):
                            result[key].append(extract_fields_from_input(input_item, actual_base_data[key][i]))
                        else:
                            result[key].append(actual_base_data[key][i])
            else:
                result[key] = actual_base_data[key]

    return result

def compare_json_objects(input_data: Dict[str, Any], base_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Compare two JSON objects and return comparison results.
    Only compares the keys specified in KEYS_TO_COMPARE or all keys in input_data if KEYS_TO_COMPARE is empty.
    """
    differences = {}
    matches = {}

    def compare_recursive(input_obj: Any, base_obj: Any, path: str = ""):
        if isinstance(input_obj, dict) and isinstance(base_obj, dict):
            # Use specified keys or all keys from input
            keys_to_check = KEYS_TO_COMPARE if KEYS_TO_COMPARE and not path else input_obj.keys()

            for key in keys_to_check:
                if key in input_obj:  # Only process keys that exist in input
                    current_path = f"{path}.{key}" if path else key
                    if key in base_obj:
                        compare_recursive(input_obj[key], base_obj[key], current_path)
                    else:
                        differences[current_path] = {
                            "input": input_obj[key],
                            "base": "KEY_NOT_FOUND"
                        }
        elif isinstance(input_obj, list) and isinstance(base_obj, list):
            for i, input_item in enumerate(input_obj):
                current_path = f"{path}[{i}]"
                if i < len(base_obj):
                    compare_recursive(input_item, base_obj[i], current_path)
                else:
                    differences[current_path] = {
                        "input": input_item,
                        "base": "INDEX_OUT_OF_RANGE"
                    }
        else:
            if input_obj == base_obj:
                matches[path] = input_obj
            else:
                differences[path] = {
                    "input": input_obj,
                    "base": base_obj
                }

    compare_recursive(input_data, base_data)

    return {
        "matches": matches,
        "differences": differences,
        "total_fields": len(matches) + len(differences),
        "match_percentage": (len(matches) / (len(matches) + len(differences)) * 100) if (len(matches) + len(differences)) > 0 else 0
    }

def process_file_pair(base_file: str, input_file: str) -> Tuple[str, str, Dict[str, Any]]:
    """
    Process a single pair of base and input JSON files.
    """
    try:
        # Load base JSON
        with open(base_file, 'r', encoding='utf-8') as f:
            base_data = json.load(f)
        
        # Load input JSON
        with open(input_file, 'r', encoding='utf-8') as f:
            input_data = json.load(f)
        
        # Convert input to lowercase
        input_data_lower = convert_to_lowercase(input_data)
        
        # Extract only fields that exist in input from base
        filtered_base_data = extract_fields_from_input(input_data_lower, base_data)
        
        # Convert filtered base data to lowercase for comparison
        filtered_base_data_lower = convert_to_lowercase(filtered_base_data)
        
        # Compare the JSONs
        comparison_result = compare_json_objects(input_data_lower, filtered_base_data_lower)
        
        logging.info(f"Processed {input_file} vs {base_file}: {comparison_result['match_percentage']:.2f}% match")
        
        return base_file, input_file, comparison_result
        
    except Exception as e:
        logging.error(f"Error processing {input_file} vs {base_file}: {str(e)}")
        return base_file, input_file, {"error": str(e)}

def process_batch(batch_pairs: List[Tuple[str, str]]) -> List[Tuple[str, str, Dict[str, Any]]]:
    """
    Process a batch of file pairs using ThreadPoolExecutor.
    """
    results = []
    with ThreadPoolExecutor(max_workers=5) as executor:
        futures = [executor.submit(process_file_pair, base_file, input_file) 
                  for base_file, input_file in batch_pairs]
        
        for future in futures:
            results.append(future.result())
    
    return results

def save_results(results: List[Tuple[str, str, Dict[str, Any]]], batch_num: int):
    """
    Save comparison results to files in the results directory.
    """
    # Save detailed results
    detailed_file = os.path.join(RESULTS_DIR, f"comparison_batch_{batch_num}_detailed.json")
    with open(detailed_file, 'w', encoding='utf-8') as f:
        json.dump([{
            "base_file": base_file,
            "input_file": input_file,
            "comparison": result
        } for base_file, input_file, result in results], f, indent=2, ensure_ascii=False)

    # Save summary results
    summary_file = os.path.join(RESULTS_DIR, f"comparison_batch_{batch_num}_summary.txt")
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write(f"Batch {batch_num} Comparison Summary\n")
        f.write("=" * 50 + "\n\n")

        for base_file, input_file, result in results:
            if "error" in result:
                f.write(f"ERROR: {input_file} vs {base_file}\n")
                f.write(f"Error: {result['error']}\n\n")
            else:
                f.write(f"File: {input_file} vs {base_file}\n")
                f.write(f"Match Percentage: {result['match_percentage']:.2f}%\n")
                f.write(f"Total Fields: {result['total_fields']}\n")
                f.write(f"Matches: {len(result['matches'])}\n")
                f.write(f"Differences: {len(result['differences'])}\n\n")

def main():
    """
    Main function to run the comparison script.
    """
    # Check if filename lists are provided
    if BASE_JSON_FILENAMES and INPUT_JSON_FILENAMES:
        # Use specified filenames with directory paths
        base_files = [os.path.join(BASE_JSONS_DIR, filename) for filename in BASE_JSON_FILENAMES]
        input_files = [os.path.join(INPUT_JSONS_DIR, filename) for filename in INPUT_JSON_FILENAMES]

        logging.info("Using specified filename lists")
        logging.info(f"Base files: {BASE_JSON_FILENAMES}")
        logging.info(f"Input files: {INPUT_JSON_FILENAMES}")

        if len(BASE_JSON_FILENAMES) != len(INPUT_JSON_FILENAMES):
            logging.error("BASE_JSON_FILENAMES and INPUT_JSON_FILENAMES must have the same length")
            return

    else:
        # Fall back to scanning directories
        logging.info("No filename lists provided, scanning directories...")
        base_files, input_files = get_json_files_from_directories()

        if not base_files:
            logging.error(f"No JSON files found in {BASE_JSONS_DIR} directory")
            return

        if not input_files:
            logging.error(f"No JSON files found in {INPUT_JSONS_DIR} directory")
            return

        if len(base_files) != len(input_files):
            logging.warning(f"Number of base files ({len(base_files)}) != number of input files ({len(input_files)})")
            logging.info("Will process pairs up to the minimum count")
            min_count = min(len(base_files), len(input_files))
            base_files = base_files[:min_count]
            input_files = input_files[:min_count]

    logging.info(f"Processing {len(base_files)} file pairs")
    logging.info(f"Base files directory: {BASE_JSONS_DIR}")
    logging.info(f"Input files directory: {INPUT_JSONS_DIR}")
    logging.info(f"Results will be saved to: {RESULTS_DIR}")

    # Create file pairs in order
    file_pairs = list(zip(base_files, input_files))

    # Process in batches of 5
    batch_size = 5
    total_batches = (len(file_pairs) + batch_size - 1) // batch_size

    logging.info(f"Starting comparison of {len(file_pairs)} file pairs in {total_batches} batches")

    for batch_num in range(total_batches):
        start_idx = batch_num * batch_size
        end_idx = min(start_idx + batch_size, len(file_pairs))
        batch_pairs = file_pairs[start_idx:end_idx]

        logging.info(f"Processing batch {batch_num + 1}/{total_batches} ({len(batch_pairs)} files)")

        # Process the batch
        results = process_batch(batch_pairs)

        # Save results
        save_results(results, batch_num + 1)

        logging.info(f"Batch {batch_num + 1} completed")

    logging.info("All comparisons completed")

if __name__ == "__main__":
    main()
