{"patient_info": {"first_name": "<PERSON><PERSON><PERSON>", "last_name": "<PERSON><PERSON>"}, "physician_info": {"first_name": "A", "last_name": "<PERSON><PERSON>"}, "medical_facility": {"facility_name": "Mount Sinai Hospital of Queens", "location": "25-10 30th Avenue, Long Island City, NY, 11102-2448"}, "is_lab_report": true, "test_date": "2023-04-27", "lab_reports": [{"name": "WBC A, 01", "result": "11.10", "range": "5.00 - 11.00", "units": "x10E3/uL", "test_type": "CBC With Differential/Platelet", "comment": null, "comment_english": null, "index": 1, "result_value_type": "numeric_value"}, {"name": "RBC A, 01", "result": "3.60", "range": "4.50 - 6.00", "units": "x10E6/uL", "test_type": "CBC With Differential/Platelet", "comment": null, "comment_english": null, "index": 2, "result_value_type": "numeric_value"}, {"name": "Hemoglobin A,01", "result": "10.7", "range": "13.9 - 16.3", "units": "G/DL", "test_type": "CBC With Differential/Platelet", "comment": null, "comment_english": null, "index": 3, "result_value_type": "numeric_value"}, {"name": "Hematocrit A, 01", "result": "33.8", "range": "41.0 - 51.0", "units": "%", "test_type": "CBC With Differential/Platelet", "comment": null, "comment_english": null, "index": 4, "result_value_type": "numeric_value"}, {"name": "MCV A, 01", "result": "93.8", "range": "80.0 - 100.0", "units": "FL", "test_type": "CBC With Differential/Platelet", "comment": null, "comment_english": null, "index": 5, "result_value_type": "numeric_value"}, {"name": "MCH A, 01", "result": "29.7", "range": "27.0 - 32.0", "units": "PG", "test_type": "CBC With Differential/Platelet", "comment": null, "comment_english": null, "index": 6, "result_value_type": "numeric_value"}, {"name": "MCHC A, 01", "result": "31.7", "range": "32.0 - 36.5", "units": "G/DL", "test_type": "CBC With Differential/Platelet", "comment": null, "comment_english": null, "index": 7, "result_value_type": "numeric_value"}, {"name": "RDW A, 01", "result": "14.2", "range": "11.5 - 14.5", "units": "%", "test_type": "CBC With Differential/Platelet", "comment": null, "comment_english": null, "index": 8, "result_value_type": "numeric_value"}, {"name": "Platelets A, 01", "result": "363", "range": "150 - 400", "units": "x10E3/uL", "test_type": "CBC With Differential/Platelet", "comment": null, "comment_english": null, "index": 9, "result_value_type": "numeric_value"}, {"name": "Neutrophils A, 01", "result": "61.4", "range": "40.0 - 78.0", "units": "%", "test_type": "CBC With Differential/Platelet", "comment": null, "comment_english": null, "index": 10, "result_value_type": "numeric_value"}, {"name": "Lymphs A, 01", "result": "20.5", "range": "15.0 - 50.0", "units": "%", "test_type": "CBC With Differential/Platelet", "comment": null, "comment_english": null, "index": 11, "result_value_type": "numeric_value"}, {"name": "Monocytes A, 01", "result": "16.0", "range": "2.0 - 11.0", "units": "%", "test_type": "CBC With Differential/Platelet", "comment": null, "comment_english": null, "index": 12, "result_value_type": "numeric_value"}, {"name": "Eos A, 01", "result": "1.0", "range": "0.0 - 5.0", "units": "%", "test_type": "CBC With Differential/Platelet", "comment": null, "comment_english": null, "index": 13, "result_value_type": "numeric_value"}, {"name": "Basos A, 01", "result": "1.1", "range": "0.0 - 1.0", "units": "%", "test_type": "CBC With Differential/Platelet", "comment": null, "comment_english": null, "index": 14, "result_value_type": "numeric_value"}, {"name": "Neutrophils (Absolute) A, 01", "result": "6.8", "range": "1.9 - 8.0", "units": "x10E3/uL", "test_type": "CBC With Differential/Platelet", "comment": null, "comment_english": null, "index": 15, "result_value_type": "numeric_value"}, {"name": "Lymphs (Absolute) A, 01", "result": "2.3", "range": "1.0 - 4.5", "units": "x10E3/uL", "test_type": "CBC With Differential/Platelet", "comment": null, "comment_english": null, "index": 16, "result_value_type": "numeric_value"}, {"name": "Monocytes(Absolute) A, 01", "result": "1.8", "range": "0.2 - 1.0", "units": "x10E3/uL", "test_type": "CBC With Differential/Platelet", "comment": null, "comment_english": null, "index": 17, "result_value_type": "numeric_value"}, {"name": "Eos (Absolute) A, 01", "result": "0.1", "range": "0.0 - 0.8", "units": "x10E3/uL", "test_type": "CBC With Differential/Platelet", "comment": null, "comment_english": null, "index": 18, "result_value_type": "numeric_value"}, {"name": "Baso (Absolute) A, 01", "result": "0.1", "range": "0.0 - 0.2", "units": "x10E3/uL", "test_type": "CBC With Differential/Platelet", "comment": null, "comment_english": null, "index": 19, "result_value_type": "numeric_value"}, {"name": "Hemoglobin A1c B, 02", "result": "6.8", "range": "4.8-5.6", "units": "%", "test_type": "Hemoglobin A1c", "comment": "Prediabetes: 5.7 - 6.4; Diabetes: >6.4; Glycemic control for adults with diabetes: <7.0", "comment_english": null, "index": 20, "result_value_type": "numeric_value"}, {"name": "Reticulocyte Count A, 01", "result": "2.5", "range": "0.5 - 2.5", "units": "%", "test_type": "Reticulocyte Count", "comment": null, "comment_english": null, "index": 21, "result_value_type": "numeric_value"}]}