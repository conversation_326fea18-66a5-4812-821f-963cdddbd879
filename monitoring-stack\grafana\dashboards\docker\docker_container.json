{"__inputs": [{"name": "DS_PROMETHEUS", "label": "Prometheus", "description": "", "type": "datasource", "pluginId": "prometheus", "pluginName": "Prometheus"}], "__requires": [{"type": "panel", "id": "singlestat", "name": "Singlestat", "version": ""}, {"type": "panel", "id": "graph", "name": "Graph", "version": ""}, {"type": "panel", "id": "table", "name": "Table", "version": ""}, {"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "4.0.0-beta2"}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}], "id": null, "title": "Docker and system monitoring", "description": "A simple overview of the most important Docker host and container metrics. (cAdvisor/Prometheus)", "tags": [], "style": "dark", "timezone": "browser", "editable": true, "sharedCrosshair": true, "hideControls": false, "time": {"from": "now-24h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "templating": {"list": [{"allValue": ".+", "current": {}, "datasource": "${DS_PROMETHEUS}", "hide": 0, "includeAll": true, "label": "Container Group", "multi": true, "name": "containergroup", "options": [], "query": "label_values(container_group)", "refresh": 1, "regex": "", "sort": 0, "tagValuesQuery": null, "tagsQuery": null, "type": "query"}, {"auto": true, "auto_count": 50, "auto_min": "50s", "current": {"text": "auto", "value": "$__auto_interval"}, "datasource": null, "hide": 0, "includeAll": false, "label": "Interval", "multi": false, "name": "interval", "options": [{"text": "auto", "value": "$__auto_interval", "selected": true}, {"text": "30s", "value": "30s", "selected": false}, {"text": "1m", "value": "1m", "selected": false}, {"text": "2m", "value": "2m", "selected": false}, {"text": "3m", "value": "3m", "selected": false}, {"text": "5m", "value": "5m", "selected": false}, {"text": "7m", "value": "7m", "selected": false}, {"text": "10m", "value": "10m", "selected": false}, {"text": "30m", "value": "30m", "selected": false}, {"text": "1h", "value": "1h", "selected": false}, {"text": "6h", "value": "6h", "selected": false}, {"text": "12h", "value": "12h", "selected": false}, {"text": "1d", "value": "1d", "selected": false}, {"text": "7d", "value": "7d", "selected": false}, {"text": "14d", "value": "14d", "selected": false}, {"text": "30d", "value": "30d", "selected": false}], "query": "30s,1m,2m,3m,5m,7m,10m,30m,1h,6h,12h,1d,7d,14d,30d", "refresh": 2, "type": "interval"}, {"allValue": null, "current": {}, "datasource": "${DS_PROMETHEUS}", "hide": 0, "includeAll": false, "label": "Node", "multi": true, "name": "server", "options": [], "query": "label_values(node_boot_time, instance)", "refresh": 1, "regex": "/([^:]+):.*/", "sort": 0, "tagValuesQuery": null, "tagsQuery": null, "type": "query"}]}, "annotations": {"list": []}, "refresh": "5m", "schemaVersion": 13, "version": 57, "links": [], "gnetId": 893, "rows": [{"title": "Dashboard Row", "panels": [{"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "datasource": "${DS_PROMETHEUS}", "decimals": 0, "editable": true, "error": false, "format": "s", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "height": "", "id": 24, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "30%", "prefix": "", "prefixFontSize": "20%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "span": 2, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "targets": [{"expr": "time() - node_boot_time{instance=~\"$server:.*\"}", "hide": false, "intervalFactor": 2, "legendFormat": "", "refId": "A", "step": 1800}], "thresholds": "", "title": "Uptime", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "datasource": "${DS_PROMETHEUS}", "editable": true, "error": false, "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "id": 31, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "span": 2, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "targets": [{"expr": "count(rate(container_last_seen{name=~\".+\"}[$interval]))", "intervalFactor": 2, "refId": "A", "step": 1800}], "thresholds": "", "title": "Containers", "type": "singlestat", "valueFontSize": "120%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(50, 172, 45, 0.97)", "rgba(237, 129, 40, 0.89)", "rgba(245, 54, 54, 0.9)"], "datasource": "${DS_PROMETHEUS}", "decimals": 1, "editable": true, "error": false, "format": "percentunit", "gauge": {"maxValue": 1, "minValue": 0, "show": true, "thresholdLabels": false, "thresholdMarkers": true}, "id": 26, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "span": 2, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "targets": [{"expr": "min((node_filesystem_size{fstype=~\"xfs|ext4\",instance=~\"$server:.*\"} - node_filesystem_free{fstype=~\"xfs|ext4\",instance=~\"$server:.*\"} )/ node_filesystem_size{fstype=~\"xfs|ext4\",instance=~\"$server:.*\"})", "hide": false, "intervalFactor": 2, "refId": "A", "step": 1800}], "thresholds": "0.75, 0.90", "title": "Disk space", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(50, 172, 45, 0.97)", "rgba(237, 129, 40, 0.89)", "rgba(245, 54, 54, 0.9)"], "datasource": "${DS_PROMETHEUS}", "decimals": 0, "editable": true, "error": false, "format": "percent", "gauge": {"maxValue": 100, "minValue": 0, "show": true, "thresholdLabels": false, "thresholdMarkers": true}, "id": 25, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "span": 2, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "targets": [{"expr": "((node_memory_MemTotal{instance=~\"$server:.*\"} - node_memory_MemAvailable{instance=~\"$server:.*\"}) / node_memory_MemTotal{instance=~\"$server:.*\"}) * 100", "intervalFactor": 2, "refId": "A", "step": 1800}], "thresholds": "70, 90", "title": "Memory", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(50, 172, 45, 0.97)", "rgba(237, 129, 40, 0.89)", "rgba(245, 54, 54, 0.9)"], "datasource": "${DS_PROMETHEUS}", "decimals": 0, "editable": true, "error": false, "format": "decbytes", "gauge": {"maxValue": 500000000, "minValue": 0, "show": true, "thresholdLabels": false, "thresholdMarkers": true}, "id": 30, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "span": 2, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "targets": [{"expr": "(node_memory_SwapTotal{instance=~'$server:.*'} - node_memory_SwapFree{instance=~'$server:.*'})", "intervalFactor": 2, "legendFormat": "", "refId": "A", "step": 1800}], "thresholds": "400000000", "title": "<PERSON><PERSON><PERSON>", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "datasource": "${DS_PROMETHEUS}", "decimals": 0, "editable": true, "error": false, "format": "percentunit", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "id": 27, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "span": 2, "sparkline": {"fillColor": "rgba(50, 189, 31, 0.18)", "full": false, "lineColor": "rgb(69, 193, 31)", "show": true}, "targets": [{"expr": "node_load1{instance=~\"$server:.*\"} / count by(job, instance)(count by(job, instance, cpu)(node_cpu{instance=~\"$server:.*\"}))", "intervalFactor": 2, "refId": "A", "step": 1800}], "thresholds": "0.8,0.9", "title": "Load", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}], "showTitle": false, "titleSize": "h6", "height": 150, "repeat": null, "repeatRowId": null, "repeatIteration": null, "collapse": false}, {"title": "New row", "panels": [{"aliasColors": {"SENT": "#BF1B00"}, "bars": false, "datasource": "${DS_PROMETHEUS}", "editable": true, "error": false, "fill": 1, "grid": {}, "id": 19, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 1, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 2, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(container_network_receive_bytes_total{id=\"/\"}[$interval])) by (id)", "intervalFactor": 2, "legendFormat": "RECEIVED", "refId": "A", "step": 600}, {"expr": "- sum(rate(container_network_transmit_bytes_total{id=\"/\"}[$interval])) by (id)", "hide": false, "intervalFactor": 2, "legendFormat": "SENT", "refId": "B", "step": 600}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Network Traffic", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "cumulative"}, "transparent": false, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": false, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}, {"aliasColors": {"{id=\"/\",instance=\"cadvisor:8080\",job=\"prometheus\"}": "#BA43A9"}, "bars": false, "datasource": "${DS_PROMETHEUS}", "editable": true, "error": false, "fill": 1, "grid": {}, "id": 5, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 2, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(rate(container_cpu_system_seconds_total[1m]))", "hide": true, "intervalFactor": 2, "legendFormat": "a", "refId": "B", "step": 120}, {"expr": "sum(rate(container_cpu_system_seconds_total{name=~\".+\"}[1m]))", "hide": true, "interval": "", "intervalFactor": 2, "legendFormat": "nur container", "refId": "F", "step": 10}, {"expr": "sum(rate(container_cpu_system_seconds_total{id=\"/\"}[1m]))", "hide": true, "interval": "", "intervalFactor": 2, "legendFormat": "nur docker host", "metric": "", "refId": "A", "step": 20}, {"expr": "sum(rate(process_cpu_seconds_total[$interval])) * 100", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "host", "metric": "", "refId": "C", "step": 600}, {"expr": "sum(rate(container_cpu_system_seconds_total{name=~\".+\"}[1m])) + sum(rate(container_cpu_system_seconds_total{id=\"/\"}[1m])) + sum(rate(process_cpu_seconds_total[1m]))", "hide": true, "intervalFactor": 2, "legendFormat": "", "refId": "D", "step": 120}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "CPU Usage", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": false, "values": []}, "yaxes": [{"format": "percent", "label": "", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}, {"alert": {"conditions": [{"evaluator": {"params": [1.25], "type": "gt"}, "query": {"params": ["A", "5m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "alerting", "frequency": "60s", "handler": 1, "name": "Panel Title alert", "noDataState": "keep_state", "notifications": [{"id": 1}]}, "aliasColors": {}, "bars": false, "datasource": "${DS_PROMETHEUS}", "decimals": 0, "editable": true, "error": false, "fill": 1, "id": 28, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 2, "stack": false, "steppedLine": false, "targets": [{"expr": "node_load1{instance=~\"$server:.*\"} / count by(job, instance)(count by(job, instance, cpu)(node_cpu{instance=~\"$server:.*\"}))", "intervalFactor": 2, "refId": "A", "step": 600}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 1.25}], "timeFrom": null, "timeShift": null, "title": "Load", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": false, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": "1.50", "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}, {"alert": {"conditions": [{"evaluator": {"params": [850000000000], "type": "gt"}, "query": {"params": ["A", "5m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "alerting", "frequency": "60s", "handler": 1, "name": "Free/Used Disk Space alert", "noDataState": "keep_state", "notifications": [{"id": 1}]}, "aliasColors": {"Belegete Festplatte": "#BF1B00", "Free Disk Space": "#7EB26D", "Used Disk Space": "#7EB26D", "{}": "#BF1B00"}, "bars": false, "datasource": "${DS_PROMETHEUS}", "editable": true, "error": false, "fill": 1, "grid": {}, "id": 13, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "Used Disk Space", "yaxis": 1}], "span": 2, "stack": true, "steppedLine": false, "targets": [{"expr": "node_filesystem_size{fstype=\"aufs\"} - node_filesystem_free{fstype=\"aufs\"}", "hide": false, "intervalFactor": 2, "legendFormat": "Used Disk Space", "refId": "A", "step": 600}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 850000000000}], "timeFrom": null, "timeShift": null, "title": "Used Disk Space", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": false, "values": []}, "yaxes": [{"format": "bytes", "label": "", "logBase": 1, "max": 1000000000000, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}, {"alert": {"conditions": [{"evaluator": {"params": [10000000000], "type": "gt"}, "query": {"params": ["A", "5m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "alerting", "frequency": "60s", "handler": 1, "name": "Available Memory alert", "noDataState": "keep_state", "notifications": [{"id": 1}]}, "aliasColors": {"Available Memory": "#7EB26D", "Unavailable Memory": "#7EB26D"}, "bars": false, "datasource": "${DS_PROMETHEUS}", "editable": true, "error": false, "fill": 1, "grid": {}, "id": 20, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 2, "stack": true, "steppedLine": false, "targets": [{"expr": "container_memory_rss{name=~\".+\"}", "hide": true, "intervalFactor": 2, "legendFormat": "{{__name__}}", "refId": "D", "step": 20}, {"expr": "sum(container_memory_rss{name=~\".+\"})", "hide": true, "intervalFactor": 2, "legendFormat": "{{__name__}}", "refId": "A", "step": 20}, {"expr": "container_memory_usage_bytes{name=~\".+\"}", "hide": true, "intervalFactor": 2, "legendFormat": "{{name}}", "refId": "B", "step": 20}, {"expr": "container_memory_rss{id=\"/\"}", "hide": true, "intervalFactor": 2, "legendFormat": "{{__name__}}", "refId": "C", "step": 20}, {"expr": "sum(container_memory_rss)", "hide": true, "intervalFactor": 2, "legendFormat": "{{__name__}}", "refId": "E", "step": 20}, {"expr": "node_memory_Buffers", "hide": true, "intervalFactor": 2, "legendFormat": "node_memory_Dirty", "refId": "N", "step": 30}, {"expr": "node_memory_MemFree", "hide": true, "intervalFactor": 2, "legendFormat": "{{__name__}}", "refId": "F", "step": 20}, {"expr": "node_memory_MemAvailable", "hide": true, "intervalFactor": 2, "legendFormat": "Available Memory", "refId": "H", "step": 20}, {"expr": "node_memory_MemTotal - node_memory_MemAvailable", "hide": false, "intervalFactor": 2, "legendFormat": "Unavailable Memory", "refId": "G", "step": 600}, {"expr": "node_memory_Inactive", "hide": true, "intervalFactor": 2, "legendFormat": "{{__name__}}", "refId": "I", "step": 30}, {"expr": "node_memory_KernelStack", "hide": true, "intervalFactor": 2, "legendFormat": "{{__name__}}", "refId": "J", "step": 30}, {"expr": "node_memory_Active", "hide": true, "intervalFactor": 2, "legendFormat": "{{__name__}}", "refId": "K", "step": 30}, {"expr": "node_memory_MemTotal - (node_memory_Active + node_memory_MemFree + node_memory_Inactive)", "hide": true, "intervalFactor": 2, "legendFormat": "Unknown", "refId": "L", "step": 40}, {"expr": "node_memory_MemFree + node_memory_Inactive ", "hide": true, "intervalFactor": 2, "legendFormat": "{{__name__}}", "refId": "M", "step": 30}, {"expr": "container_memory_rss{name=~\".+\"}", "hide": true, "intervalFactor": 2, "legendFormat": "{{__name__}}", "refId": "O", "step": 30}, {"expr": "node_memory_Inactive + node_memory_MemFree + node_memory_MemAvailable", "hide": true, "intervalFactor": 2, "legendFormat": "", "refId": "P", "step": 40}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 10000000000}], "timeFrom": null, "timeShift": null, "title": "Available Memory", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": false, "values": []}, "yaxes": [{"format": "bytes", "label": "", "logBase": 1, "max": 16000000000, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}, {"aliasColors": {"IN on /sda": "#7EB26D", "OUT on /sda": "#890F02"}, "bars": false, "datasource": "${DS_PROMETHEUS}", "editable": true, "error": false, "fill": 1, "grid": {}, "id": 3, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 2, "stack": false, "steppedLine": false, "targets": [{"expr": "-sum(rate(node_disk_bytes_read[$interval])) by (device)", "hide": false, "intervalFactor": 2, "legendFormat": "OUT on /{{device}}", "metric": "node_disk_bytes_read", "refId": "A", "step": 600}, {"expr": "sum(rate(node_disk_bytes_written[$interval])) by (device)", "intervalFactor": 2, "legendFormat": "IN on /{{device}}", "metric": "", "refId": "B", "step": 600}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Disk I/O", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": false, "values": []}, "yaxes": [{"format": "Bps", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}], "showTitle": false, "titleSize": "h6", "height": 202, "repeat": null, "repeatRowId": null, "repeatIteration": null, "collapse": false}, {"title": "New row", "panels": [{"aliasColors": {}, "bars": false, "datasource": "${DS_PROMETHEUS}", "editable": true, "error": false, "fill": 1, "grid": {}, "id": 8, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(container_network_receive_bytes_total{name=~\".+\"}[$interval])) by (name)", "intervalFactor": 2, "legendFormat": "{{name}}", "refId": "A", "step": 240}, {"expr": "- rate(container_network_transmit_bytes_total{name=~\".+\"}[$interval])", "hide": true, "intervalFactor": 2, "legendFormat": "{{name}}", "refId": "B", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Received Network Traffic per Container", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "cumulative"}, "transparent": false, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "Bps", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_PROMETHEUS}", "editable": true, "error": false, "fill": 1, "grid": {}, "id": 9, "legend": {"alignAsTable": true, "avg": false, "current": false, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(container_network_transmit_bytes_total{name=~\".+\"}[$interval])) by (name)", "intervalFactor": 2, "legendFormat": "{{name}}", "refId": "A", "step": 240}, {"expr": "rate(container_network_transmit_bytes_total{id=\"/\"}[$interval])", "hide": true, "intervalFactor": 2, "legendFormat": "", "refId": "B", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Sent Network Traffic per Container", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "cumulative"}, "transparent": false, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "Bps", "label": "", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": "", "logBase": 10, "max": 8, "min": 0, "show": false}]}], "showTitle": false, "titleSize": "h6", "height": 251, "repeat": null, "repeatRowId": null, "repeatIteration": null, "collapse": false}, {"title": "Row", "panels": [{"aliasColors": {}, "bars": false, "datasource": "${DS_PROMETHEUS}", "editable": true, "error": false, "fill": 5, "grid": {}, "id": 1, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(rate(container_cpu_usage_seconds_total{name=~\".+\"}[$interval])) by (name) * 100", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{name}}", "metric": "", "refId": "F", "step": 240}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "CPU Usage per Container", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percent", "label": "", "logBase": 1, "max": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}], "showTitle": false, "titleSize": "h6", "height": 247, "repeat": null, "repeatRowId": null, "repeatIteration": null, "collapse": false}, {"title": "Dashboard Row", "panels": [{"aliasColors": {}, "bars": false, "datasource": "${DS_PROMETHEUS}", "editable": true, "error": false, "fill": 3, "grid": {}, "id": 10, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(container_memory_rss{name=~\".+\"}) by (name)", "hide": false, "intervalFactor": 2, "legendFormat": "{{name}}", "refId": "A", "step": 240}, {"expr": "container_memory_usage_bytes{name=~\".+\"}", "hide": true, "intervalFactor": 2, "legendFormat": "{{name}}", "refId": "B", "step": 240}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Memory Usage per Container", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": "", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "datasource": "${DS_PROMETHEUS}", "editable": true, "error": false, "fill": 3, "grid": {}, "id": 34, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "span": 6, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(container_memory_swap{name=~\".+\"}) by (name)", "hide": false, "intervalFactor": 2, "legendFormat": "{{name}}", "refId": "A", "step": 240}, {"expr": "container_memory_usage_bytes{name=~\".+\"}", "hide": true, "intervalFactor": 2, "legendFormat": "{{name}}", "refId": "B", "step": 240}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Memory Swap per Container", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": "", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}], "showTitle": false, "titleSize": "h6", "height": 250, "repeat": null, "repeatRowId": null, "repeatIteration": null, "collapse": false}, {"title": "Dashboard Row", "panels": [{"columns": [{"text": "Current", "value": "current"}], "editable": true, "error": false, "fontSize": "100%", "id": 37, "links": [], "pageSize": null, "scroll": true, "showHeader": true, "sort": {"col": 0, "desc": true}, "span": 4, "styles": [{"colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "thresholds": ["10000000", " 25000000"], "type": "number", "unit": "decbytes"}], "targets": [{"expr": "sum(container_spec_memory_limit_bytes{name=~\".+\"} - container_memory_usage_bytes{name=~\".+\"}) by (name) ", "hide": true, "intervalFactor": 2, "legendFormat": "{{name}}", "metric": "", "refId": "A", "step": 240}, {"expr": "sum(container_spec_memory_limit_bytes{name=~\".+\"}) by (name) ", "hide": true, "intervalFactor": 2, "legendFormat": "{{name}}", "refId": "B", "step": 240}, {"expr": "container_memory_usage_bytes{name=~\".+\"}", "hide": false, "intervalFactor": 2, "legendFormat": "{{name}}", "refId": "C", "step": 240}], "title": "Usage memory", "transform": "timeseries_aggregations", "type": "table"}, {"columns": [{"text": "Current", "value": "current"}], "editable": true, "error": false, "fontSize": "100%", "id": 35, "links": [], "pageSize": null, "scroll": true, "showHeader": true, "sort": {"col": 1, "desc": true}, "span": 4, "styles": [{"colorMode": "cell", "colors": ["rgba(50, 172, 45, 0.97)", "rgba(237, 129, 40, 0.89)", "rgba(245, 54, 54, 0.9)"], "decimals": 2, "pattern": "/.*/", "thresholds": ["80", "90"], "type": "number", "unit": "percent"}], "targets": [{"expr": "sum(100 - ((container_spec_memory_limit_bytes{name=~\".+\"} - container_memory_usage_bytes{name=~\".+\"})  * 100 / container_spec_memory_limit_bytes{name=~\".+\"}) ) by (name) ", "intervalFactor": 2, "legendFormat": "{{name}}", "metric": "", "refId": "A", "step": 240}, {"expr": "sum(container_spec_memory_limit_bytes{name=~\".+\"}) by (name) ", "hide": true, "intervalFactor": 2, "legendFormat": "{{name}}", "refId": "B", "step": 240}, {"expr": "container_memory_usage_bytes{name=~\".+\"}", "hide": true, "intervalFactor": 2, "legendFormat": "{{name}}", "refId": "C", "step": 240}], "title": "Remaining memory", "transform": "timeseries_aggregations", "type": "table"}, {"columns": [{"text": "Current", "value": "current"}], "editable": true, "error": false, "fontSize": "100%", "id": 36, "links": [], "pageSize": null, "scroll": true, "showHeader": true, "sort": {"col": 0, "desc": true}, "span": 4, "styles": [{"colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "thresholds": ["10000000", " 25000000"], "type": "number", "unit": "decbytes"}], "targets": [{"expr": "sum(container_spec_memory_limit_bytes{name=~\".+\"} - container_memory_usage_bytes{name=~\".+\"}) by (name) ", "hide": true, "intervalFactor": 2, "legendFormat": "{{name}}", "metric": "", "refId": "A", "step": 240}, {"expr": "sum(container_spec_memory_limit_bytes{name=~\".+\"}) by (name) ", "hide": false, "intervalFactor": 2, "legendFormat": "{{name}}", "refId": "B", "step": 240}, {"expr": "container_memory_usage_bytes{name=~\".+\"}", "hide": true, "intervalFactor": 2, "legendFormat": "{{name}}", "refId": "C", "step": 240}], "title": "Limit memory", "transform": "timeseries_aggregations", "type": "table"}], "showTitle": false, "titleSize": "h6", "height": 361, "repeat": null, "repeatRowId": null, "repeatIteration": null, "collapse": false}]}