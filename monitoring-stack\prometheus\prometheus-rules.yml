groups:
- name: resource_alerts
  rules:
  # CPU Utilization Alert
  - alert: HighCPUUsage
    expr: |
      (1 - avg(irate(node_cpu_seconds_total{mode="idle"}[5m])) by (instance)) * 100 > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: High CPU usage detected (instance {{ $labels.instance }})
      description: CPU usage is above 80%
      resolution_message: "CPU utilization has decreased below 80% and returned to normal operational levels"

  # Container Resource Usage Alerts
  - alert: ContainerCPUUsageAboveRequest
    expr: |
      (container_cpu_usage_seconds_total{container="ocr",name="app"} - ignoring(container, pod) group_left container_spec_cpu_quota{container="ocr",name="app"} > 0)
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: Container CPU usage above request (container={{ $labels.container }})
      description: Container CPU usage is above the requested amount
      resolution_message: "Container CPU usage has returned within the requested resource limits"

  - alert: ContainerMemoryUsageAboveRequest
    expr: |
      container_memory_working_set_bytes{container="ocr",name="app"} > container_spec_memory_limit_bytes{container="ocr",name="app"}
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: Container memory usage above request (container={{ $labels.container }})
      description: Container memory usage is above the requested amount
      resolution_message: "Container memory usage has decreased and is now within the requested limits"

  - alert: ContainerCPUThrottling
    expr: |
      rate(container_cpu_cfs_throttled_seconds_total{container="ocr",name="app"}[5m]) > 0
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: Container CPU throttling detected (container={{ $labels.container }})
      description: Container is being throttled due to CPU limit
      resolution_message: "Container CPU throttling has stopped and the container is now running within its CPU limits"