import redis
import json
from typing import Any, List, Optional
from app.model.ocr_model import (
    ReportStatus,
    StageDataSummary,
    StageDataPhysician,
    StageDataInstitution,
    StageDataLabReport,
    StageReport,
    ProcessingStage,
    StageStatus,
)
from app.util.logger import logger
from app.core.config import settings


class RedisSessionManager:
    def __init__(
        self,
        session_id: str,
        redis_config: dict = {
            "host": settings.REDIS_HOST,
            "port": settings.REDIS_PORT,
            "db": settings.REDIS_DB,
        },
    ):

        # Initialize Redis connection
        self.redis = redis.StrictRedis(
            host=redis_config.get("host"),
            port=redis_config.get("port"),
            db=redis_config.get("db"),
            decode_responses=True,
        )
        self.session_id = session_id
        logger.info(f"Initializing Redis session manager for session {session_id}")

        # Create session if it doesn't exist
        if not self.redis.exists(self.session_id):
            initial_data = ReportStatus(
                user_id="",
                document_summary=StageDataSummary(
                    progress=0, status=StageStatus.PENDING, data=None
                ),
                matched_physician=StageDataPhysician(
                    progress=0, status=StageStatus.PENDING, data=None
                ),
                matched_institution=StageDataInstitution(
                    progress=0, status=StageStatus.PENDING, data=None
                ),
                lab_reports=StageDataLabReport(
                    progress=0, status=StageStatus.PENDING, data=[]
                ),
                analysis=StageReport(
                    stage=ProcessingStage.DOCUMENT_SUMMARY, message=""
                ),
            ).dict()
            self.redis.set(self.session_id, json.dumps(initial_data))
            logger.info(f"Created new session with ID {session_id}")

    def create_session(self, session_data: dict) -> bool:
        """Create a new session with a passed or generated ID and save initial session data."""
        try:
            logger.info(f"Creating new session with ID {self.session_id}")
            # Convert to ReportStatus model
            report_status = ReportStatus(**session_data)

            # Store session data as JSON
            self.redis.set(self.session_id, json.dumps(report_status.dict()))
            logger.info(f"Successfully created session {self.session_id}")
            return True
        except Exception as e:
            logger.exception(e)
            return False

    def get_all_sessions(self) -> List[str]:
        """Get a list of all available session IDs in Redis.

        Returns:
            List[str]: List of session IDs stored in Redis
        """
        try:
            logger.info("Retrieving all session IDs")
            # Get all keys from Redis
            all_keys = self.redis.keys("*")
            logger.info(f"Found {len(all_keys)} sessions")
            return all_keys
        except Exception as e:
            logger.exception(e)
            return []

    def get_session(self) -> Optional[ReportStatus]:
        """Get or create a session and return its history by session_id."""
        try:
            logger.info(f"Retrieving session data for {self.session_id}")
            session_data = self.redis.get(self.session_id)
            if session_data:
                session_dict = json.loads(session_data)
                # Convert any None values to empty lists for lab_reports data
                if session_dict.get("lab_reports", {}).get("data") is None:
                    session_dict["lab_reports"]["data"] = []
                logger.info(f"Successfully retrieved session {self.session_id}")
                return ReportStatus(**session_dict)
            else:
                logger.info(
                    f"No existing session found for {self.session_id}, creating new session"
                )
                # Create a new session if it doesn't exist
                initial_data = ReportStatus(
                    user_id="",
                    document_summary=StageDataSummary(
                        progress=0, status=StageStatus.PENDING, data=None
                    ),
                    matched_physician=StageDataPhysician(
                        progress=0, status=StageStatus.PENDING, data=None
                    ),
                    matched_institution=StageDataInstitution(
                        progress=0, status=StageStatus.PENDING, data=None
                    ),
                    lab_reports=StageDataLabReport(
                        progress=0, status=StageStatus.PENDING, data=[]
                    ),
                    analysis=StageReport(
                        stage=ProcessingStage.DOCUMENT_SUMMARY, message=""
                    ),
                )
                self.create_session(initial_data.dict())
                return initial_data
        except Exception as e:
            logger.exception(e)
            return None

    def update_session_data(self, key: str, data: Any) -> bool:
        """Update a specific key in the data field of a session.

        Args:
            key: The key in the data dict to update
            data: The value to set for the key
        """
        try:
            logger.info(f"Updating session {self.session_id} with key: {key}")
            session_data = self.get_session()
            if session_data:
                # Update the specified field
                setattr(session_data, key, data)
                self.redis.set(self.session_id, json.dumps(session_data.dict()))
                logger.info(f"Successfully updated session {self.session_id}")
                return True
            logger.warning(f"Session {self.session_id} not found for update")
            return False
        except Exception as e:
            logger.exception(e)
            return False

    def delete_session(self) -> bool:
        """Remove session from session list using session ID."""
        try:
            logger.info(f"Deleting session {self.session_id}")
            self.redis.delete(self.session_id)
            logger.info(f"Successfully deleted session {self.session_id}")
            return True
        except Exception as e:
            logger.exception(e)
            return False
