{"user_id": "288", "document_summary": {"progress": 100, "status": "complete", "data": {"is_lab_report": true, "patient_info": {"first_name": "אילנה", "last_name": "אלגרבלי"}, "physician_info": {"first_name": "אירנה", "last_name": "אלון"}, "medical_facility": {"facility_name": null, "location": null}, "test_date": "2023-06-08T00:00:00", "lab_reports": [{"name": "Glucose (B)", "result": 81, "range": "70-100", "units": "mg/dl", "test_type": "כימיה בדם", "comment": "מיום 20.1.23 חל שינוי בערכת הבדיקה", "comment_english": "From 20.1.23 there is a change in the test kit", "index": 0, "result_value_type": "numeric_value"}, {"name": "Urea (B)", "result": 31, "range": "19-49", "units": "mg/dl", "test_type": "כימיה בדם", "comment": "מיום 20.1.23 חל שינוי בערכת הבדיקה. שים לב לשינוי בערכים המודדים וערכי הייחוס", "comment_english": "From 20.1.23 there is a change in the test kit. Pay attention to the change in measured values and reference values", "index": 1, "result_value_type": "numeric_value"}, {"name": "<PERSON><PERSON>tinine (B)", "result": 0.74, "range": "0.5-1", "units": "mg/dl", "test_type": "כימיה בדם", "comment": "מיום 20.1.23 חל שינוי בערכת הבדיקה. שים לב לשינוי בערכי הייחוס", "comment_english": "From 20.1.23 there is a change in the test kit. Pay attention to the change in reference values", "index": 2, "result_value_type": "numeric_value"}, {"name": "eGFR", "result": 94, "range": null, "units": "ml/min/1.73m²", "test_type": "כימיה בדם", "comment": "ערך רצוי מעל 60.", "comment_english": "Desirable value above 60.", "index": 3, "result_value_type": "numeric_value"}, {"name": "K+ Potassium (B)", "result": 4, "range": "3.5-5.1", "units": "mmol/l", "test_type": "כימיה בדם", "comment": "מיום 20.1.23 חל שינוי בערכת הבדיקה", "comment_english": "From 20.1.23 there is a change in the test kit", "index": 4, "result_value_type": "numeric_value"}, {"name": "Na- Sodium (B)", "result": 139, "range": "136-146", "units": "mmol/l", "test_type": "כימיה בדם", "comment": "מיום 20.1.23 חל שינוי בערכת הבדיקה", "comment_english": "From 20.1.23 there is a change in the test kit", "index": 5, "result_value_type": "numeric_value"}, {"name": "ALKP-Alkaline Phosphatase", "result": 57, "range": "30-120", "units": "u/l", "test_type": "כימיה בדם", "comment": "מיום 20.1.23 חל שינוי בערכת הבדיקה", "comment_english": "From 20.1.23 there is a change in the test kit", "index": 6, "result_value_type": "numeric_value"}, {"name": "ALT (GPT)", "result": 11, "range": "0-31", "units": "U/l", "test_type": "כימיה בדם", "comment": "מיום 20.1.23 חל שינוי בערכת הבדיקה", "comment_english": "From 20.1.23 there is a change in the test kit", "index": 7, "result_value_type": "numeric_value"}, {"name": "AST (GOT)", "result": 17, "range": "10-31", "units": "U/l", "test_type": "כימיה בדם", "comment": "תרום 20.1.23 חל שינוי בערכת הבדיקה", "comment_english": "From 20.1.23 there is a change in the test kit", "index": 8, "result_value_type": "numeric_value"}, {"name": "Fe - Iron", "result": 88, "range": "50-170", "units": "micg/dl", "test_type": "כימיה בדם", "comment": "מיום 22.3.23 חל שינוי בערכת הבדיקה. שים לב לשינוי בערכי הייחוס.", "comment_english": "From 22.3.23 there is a change in the test kit. Pay attention to the change in reference values.", "index": 9, "result_value_type": "numeric_value"}, {"name": "<PERSON><PERSON><PERSON>", "result": 34, "range": "10-291", "units": "ng/ml", "test_type": "כימיה בדם", "comment": "מיום 3.4.23 חל שינוי בערכות הבדיקה. שים לב לשינוי בערכי הייחוס.", "comment_english": "From 3.4.23 there is a change in the test kits. Pay attention to the change in reference values.", "index": 10, "result_value_type": "numeric_value"}, {"name": "Folic Acid", "result": 10, "range": ">5.4", "units": "ng/ml", "test_type": "כימיה בדם", "comment": "ערכי ייחוס: נורמה: גדול מ-5.4 ng/ml גבולי: 3.4 - 5.4 ng/ml", "comment_english": "Reference values: Normal: greater than 5.4 ng/ml Borderline: 3.4 - 5.4 ng/ml", "index": 11, "result_value_type": "numeric_value"}, {"name": "WBC-Leucocytes", "result": 6.3, "range": "4.5-11", "units": "10^3/micl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 12, "result_value_type": "numeric_value"}, {"name": "RBC-Red Blood Cells", "result": 4.4, "range": "4-5.2", "units": "10^6/micl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 13, "result_value_type": "numeric_value"}, {"name": "Hemoglobin", "result": 13.8, "range": "12.5-16", "units": "g/dl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 14, "result_value_type": "numeric_value"}, {"name": "Hematocrit", "result": 40.1, "range": "36-46", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 15, "result_value_type": "numeric_value"}, {"name": "MCV-Mean Cell Volume", "result": 91, "range": "79-97", "units": "fl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 16, "result_value_type": "numeric_value"}, {"name": "MCH-Mean Cell Hemoglobin", "result": 31.4, "range": "27-34", "units": "pg/cell", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 17, "result_value_type": "numeric_value"}, {"name": "MCHC-M.Cell Hb cont.", "result": 34.4, "range": "31-36", "units": "g/dl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 18, "result_value_type": "numeric_value"}, {"name": "RDW-Red Cell Distri.Width", "result": 12.8, "range": "11.6-15", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 19, "result_value_type": "numeric_value"}, {"name": "Platelets", "result": 218, "range": "150-450", "units": "10^3/micl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 20, "result_value_type": "numeric_value"}, {"name": "MPV-Mean Platelet Volume", "result": 10, "range": "8.5-12.9", "units": "fl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 21, "result_value_type": "numeric_value"}, {"name": "Neutrophils %", "result": 35.9, "range": "40-75", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 22, "result_value_type": "numeric_value"}, {"name": "Lymphocytes %", "result": 53.7, "range": "22-43", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 23, "result_value_type": "numeric_value"}, {"name": "Monocytes %", "result": 7.2, "range": "3-13", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 24, "result_value_type": "numeric_value"}, {"name": "Eosinophils %", "result": 2.7, "range": "0-6", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 25, "result_value_type": "numeric_value"}, {"name": "Basophils %", "result": 0.5, "range": "0-2", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 26, "result_value_type": "numeric_value"}, {"name": "Neutrophils #", "result": 2.25, "range": "1.8-7.7", "units": "10^3/micl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 27, "result_value_type": "numeric_value"}, {"name": "Lymphocytes #", "result": 3.36, "range": "1-4.8", "units": "10^3/micl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 28, "result_value_type": "numeric_value"}, {"name": "Monocytes #", "result": 0.45, "range": "0-1.1", "units": "10^3/micl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 29, "result_value_type": "numeric_value"}, {"name": "Eosinophils #", "result": 0.17, "range": "0-0.6", "units": "10^3/micl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 30, "result_value_type": "numeric_value"}, {"name": "Basophils #", "result": 0.03, "range": "0-0.2", "units": "10^3/micl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 31, "result_value_type": "numeric_value"}, {"name": "PT (Seconds)", "result": 10.3, "range": "9.5-11.3", "units": "Sec.", "test_type": "קרישה", "comment": null, "comment_english": null, "index": 32, "result_value_type": "numeric_value"}, {"name": "I.N.R", "result": 1, "range": null, "units": null, "test_type": "קרישה", "comment": "נבדקים עם תסמונת אנטיפוספוליפידית (Antiphospholipid syndrome) עלולה להיות הארכה של תוצאות PT (Seconds) ו-INR.", "comment_english": "Patients with Antiphospholipid syndrome may have prolonged PT (Seconds) and INR results.", "index": 33, "result_value_type": "numeric_value"}, {"name": "PTT", "result": 23.5, "range": "21.6-28.7", "units": "sec.", "test_type": "קרישה", "comment": null, "comment_english": null, "index": 34, "result_value_type": "numeric_value"}, {"name": "TSH", "result": 2.13, "range": "0.5-4.8", "units": "miu/l", "test_type": "אנדוקרינולוגיה", "comment": "החל מתאריך 18.9.22 חל שינוי בערכת הבדיקה. שים לב לערכון ערכי הייחוס.", "comment_english": "From 18.9.22 there is a change in the test kit. Pay attention to the update in reference values.", "index": 35, "result_value_type": "numeric_value"}]}}, "matched_physician": {"progress": 100, "status": "complete", "data": {"matched_id": null, "matched_title": null, "matched_name": null, "matched_lastname": null, "match_info": {"match_score": "Unknown", "reason": "No matching physician data found for given input."}}}, "matched_institution": {"progress": 100, "status": "complete", "data": {"value_name": null, "matched_display_name": null, "matched_id": null, "match_info": {"match_score": "Unknown", "reason": "Facility data is not found in uploaded document"}}}, "lab_reports": {"progress": 100, "status": "complete", "data": [{"match_data": {"id": 117, "test_type": "Complete Blood Count (CBC)", "parameter": "<PERSON><PERSON><PERSON><PERSON> (HCT)", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter name 'Hematocrit' matches 'Hematocrit (HCT)' exactly with no discrepancies."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The test type from the lab report 'המטולוגיה- ספירת דם - מכ<PERSON><PERSON><PERSON>' closely resembles 'Complete Blood Count (CBC)', indicating a similar purpose."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 40.1, "unit": "%"}, "conversion_details": null, "comment": "The unit '%' is the same as the reference unit."}, "test_params": {"name": "Hematocrit", "result": 40.1, "range": "36-46", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 15, "result_value_type": "numeric_value"}}, {"match_data": {"id": 124, "test_type": "Complete Blood Count (CBC)", "parameter": "Mean Platelet Volume (MPV)", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter name 'MPV-Mean Platelet Volume' closely matches 'Mean Platelet Volume (MPV)' with minor formatting differences."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The test type 'המטולוגיה- ספירת דם - מכ<PERSON><PERSON><PERSON>' resembles 'Complete Blood Count (CBC)' with minor variations in language."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 10, "unit": "fl"}, "conversion_details": null, "comment": "The unit 'fl' is the same as the reference unit."}, "test_params": {"name": "MPV-Mean Platelet Volume", "result": 10, "range": "8.5-12.9", "units": "fl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 21, "result_value_type": "numeric_value"}}, {"match_data": {"id": 133, "test_type": "Complete Blood Count (CBC)", "parameter": "RDW", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter 'RDW-Red Cell Distri.Width' matches with 'RDW' in the context, considering possible variations in the description."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The test type 'המטולוגיה- ספירת דם - מכש<PERSON><PERSON>' matches with 'Complete Blood Count (CBC)', indicating a direct correspondence between the two."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 12.8, "unit": "%"}, "conversion_details": {"factor": null, "calculation": null}, "comment": "No conversion was necessary as the original unit matches the reference unit."}, "test_params": {"name": "RDW-Red Cell Distri.Width", "result": 12.8, "range": "11.6-15", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 19, "result_value_type": "numeric_value"}}, {"match_data": {"id": 536, "test_type": "Complete Blood Count (CBC)", "parameter": "MCVr", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "Parameter name 'MCV-Mean Cell Volume' closely matches 'MCVr', with minor formatting differences."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "Test type 'המטולוגיה- ספירת דם - מכש<PERSON><PERSON>' is a variation of 'Complete Blood Count (CBC)', indicating a similar hematological test."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 91, "unit": "fL"}, "conversion_details": null, "comment": "Result is already in the reference unit."}, "test_params": {"name": "MCV-Mean Cell Volume", "result": 91, "range": "79-97", "units": "fl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 16, "result_value_type": "numeric_value"}}, {"match_data": {"id": 443, "test_type": "Metabolic Panel", "parameter": "Potassium (K) - blood", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter 'K+ Potassium (B)' from the lab report matches closely with 'Potassium (K) - blood' from the database, with a minor difference in naming convention (B vs. blood)."}, "match_test_type_info": {"match_score": "Unknown", "reason": "The test type 'כימיה בדם' does not have a direct match in the database for the test type name."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 4, "unit": "mmol/L"}, "conversion_details": null, "comment": "The unit 'mmol/l' is the same as the reference unit 'mmol/L', so no conversion was necessary."}, "test_params": {"name": "K+ Potassium (B)", "result": 4, "range": "3.5-5.1", "units": "mmol/l", "test_type": "כימיה בדם", "comment": "מיום 20.1.23 חל שינוי בערכת הבדיקה", "comment_english": "From 20.1.23 there is a change in the test kit", "index": 4, "result_value_type": "numeric_value"}}, {"match_data": {"id": 131, "test_type": "Complete Blood Count (CBC)", "parameter": "Platelet count (PLT)", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter 'Platelets' from the lab report is conceptually similar to 'Platelet count (PLT)', with minor typographical variations."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The test type 'המטולוגיה- ספירת דם - מכ<PERSON><PERSON><PERSON>' has a similar concept to 'Complete Blood Count (CBC)', but is different in wording and language."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 218, "unit": "10e3/microL"}, "conversion_details": {"factor": null, "calculation": null}, "comment": "The result is already in the reference unit."}, "test_params": {"name": "Platelets", "result": 218, "range": "150-450", "units": "10^3/micl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 20, "result_value_type": "numeric_value"}}, {"match_data": {"id": 385, "test_type": "Metabolic Panel", "parameter": "eGFR", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter eGFR matches conceptually with the indexed parameter eGFR despite potential formatting differences."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The test type 'כימיה בדם' (Blood Chemistry) is conceptually aligned with 'Metabolic Panel', despite a language difference."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 94, "unit": "ml/min/1.73m²"}, "conversion_details": null, "comment": "Result is already in the reference unit."}, "test_params": {"name": "eGFR", "result": 94, "range": null, "units": "ml/min/1.73m²", "test_type": "כימיה בדם", "comment": "ערך רצוי מעל 60.", "comment_english": "Desirable value above 60.", "index": 3, "result_value_type": "numeric_value"}}, {"match_data": {"id": 115, "test_type": "Complete Blood Count (CBC)", "parameter": "Eosinophils % (EOS%)", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter 'Eosinophils %' closely matches 'Eosinophils % (EOS%)' despite minor formatting differences."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The test type 'hematology - complete blood count - device' closely resembles 'Complete Blood Count (CBC)', establishing a match."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 2.7, "unit": "%"}, "conversion_details": null, "comment": "The unit is the same as the reference unit, so no conversion was necessary."}, "test_params": {"name": "Eosinophils %", "result": 2.7, "range": "0-6", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 25, "result_value_type": "numeric_value"}}, {"match_data": {"id": 455, "test_type": "Metabolic Panel", "parameter": "Sodium (Na) - blood", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter name 'Na- Sodium (B)' matches closely with 'Sodium (Na) - blood' with minor variations in naming."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The test type 'כימיה בדם' translates to 'Metabolic Panel,' indicating they are related despite the language difference."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 139, "unit": "mmol/L"}, "conversion_details": null, "comment": "The unit in the lab report is the same as the reference unit."}, "test_params": {"name": "Na- Sodium (B)", "result": 139, "range": "136-146", "units": "mmol/l", "test_type": "כימיה בדם", "comment": "מיום 20.1.23 חל שינוי בערכת הבדיקה", "comment_english": "From 20.1.23 there is a change in the test kit", "index": 5, "result_value_type": "numeric_value"}}, {"match_data": {"id": 390, "test_type": "Metabolic Panel", "parameter": "Folic acid - blood (FolA)", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter name in the lab report matches the database entry with no significant differences."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The test type in the lab report loosely matches the 'Metabolic Panel' test type in the database, considering translational differences."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 10, "unit": "ng/ml"}, "conversion_details": {"factor": null, "calculation": null}, "comment": "Result unit is already in the standardized unit. No conversion necessary."}, "test_params": {"name": "Folic Acid", "result": 10, "range": ">5.4", "units": "ng/ml", "test_type": "כימיה בדם", "comment": "ערכי ייחוס: נורמה: גדול מ-5.4 ng/ml גבולי: 3.4 - 5.4 ng/ml", "comment_english": "Reference values: Normal: greater than 5.4 ng/ml Borderline: 3.4 - 5.4 ng/ml", "index": 11, "result_value_type": "numeric_value"}}, {"match_data": {"id": 116, "test_type": "Complete Blood Count (CBC)", "parameter": "Eosinophils No. (EOS#)", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter matches with a minor spelling difference (no differences in case or punctuation) in the lab report."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The test type is conceptually similar, both referring to blood count analysis."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 0.17, "unit": "10e3/microL"}, "conversion_details": {"factor": null, "calculation": null}, "comment": "The original unit converts directly to the reference unit without alteration."}, "test_params": {"name": "Eosinophils #", "result": 0.17, "range": "0-0.6", "units": "10^3/micl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 30, "result_value_type": "numeric_value"}}, {"match_data": {"id": 337, "test_type": "Metabolic Panel", "parameter": "Alanine amino transferase, ALT (GPT) - blood", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter name matches with a minor case difference. 'U/l' from the report is similar to the context unit 'U/L'."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The test type matches conceptually, though the report has it in a different language (Hebrew)."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 11, "unit": "U/L"}, "conversion_details": {"factor": null, "calculation": null}, "comment": "The result is already in the reference unit."}, "test_params": {"name": "ALT (GPT)", "result": 11, "range": "0-31", "units": "U/l", "test_type": "כימיה בדם", "comment": "מיום 20.1.23 חל שינוי בערכת הבדיקה", "comment_english": "From 20.1.23 there is a change in the test kit", "index": 7, "result_value_type": "numeric_value"}}, {"match_data": {"id": 125, "test_type": "Complete Blood Count (CBC)", "parameter": "Monocytes % (MONO%)", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter name matches, with variations in punctuation and spacing."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The test type matches conceptually with minor differences in language."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 7.2, "unit": "%"}, "conversion_details": null, "comment": "The unit of measurement is the same, no conversion needed."}, "test_params": {"name": "Monocytes %", "result": 7.2, "range": "3-13", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 24, "result_value_type": "numeric_value"}}, {"match_data": {"id": 113, "test_type": "Complete Blood Count (CBC)", "parameter": "Basophils % (BASO%)", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter name matches conceptually with minor variations."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The test type has minor differences in capitalization but matches conceptually."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 0.5, "unit": "%"}, "conversion_details": null, "comment": "The unit is the same as the reference unit, so no conversion was necessary."}, "test_params": {"name": "Basophils %", "result": 0.5, "range": "0-2", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 26, "result_value_type": "numeric_value"}}, {"match_data": {"id": 389, "test_type": "Metabolic Panel", "parameter": "Fe<PERSON>tin (Fer) - blood", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "Exact match in the parameter name and same test type."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "Test type matches after minor case variation."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 34, "unit": "ng/ml"}, "conversion_details": null, "comment": "No conversion necessary as the unit matches the reference."}, "test_params": {"name": "<PERSON><PERSON><PERSON>", "result": 34, "range": "10-291", "units": "ng/ml", "test_type": "כימיה בדם", "comment": "מיום 3.4.23 חל שינוי בערכות הבדיקה. שים לב לשינוי בערכי הייחוס.", "comment_english": "From 3.4.23 there is a change in the test kits. Pay attention to the change in reference values.", "index": 10, "result_value_type": "numeric_value"}}, {"match_data": {"id": 136, "test_type": "Complete Blood Count (CBC)", "parameter": "WBC", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter name 'WBC-Leucocytes' matches 'WBC' with a similar concept considering 'Leucocytes' refers to the same white blood cell measure."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The test type 'המטולוגיה- ספירת דם - מכ<PERSON><PERSON><PERSON>' approximates to 'Complete Blood Count (CBC)'. Even though it's not an exact match in naming, it falls within the hematology context."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 6.3, "unit": "10e3/microL"}, "conversion_details": {"factor": null, "calculation": null}, "comment": "The unit was converted successfully as it matches the reference unit."}, "test_params": {"name": "WBC-Leucocytes", "result": 6.3, "range": "4.5-11", "units": "10^3/micl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 12, "result_value_type": "numeric_value"}}, {"match_data": {"id": 93, "test_type": "Coagulation panel", "parameter": "PTT sec (PTT)", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "Exact match for the parameter name PTT sec (PTT) found."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "Exact match for the test type under Coagulation panel."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 23.5, "unit": "Sec"}, "conversion_details": null, "comment": "Unit is the same as reference unit, no conversion needed."}, "test_params": {"name": "PTT", "result": 23.5, "range": "21.6-28.7", "units": "sec.", "test_type": "קרישה", "comment": null, "comment_english": null, "index": 34, "result_value_type": "numeric_value"}}, {"match_data": {"id": 132, "test_type": "Complete Blood Count (CBC)", "parameter": "RBC", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The lab report parameter RBC matches conceptually with the index parameter RBC for Complete Blood Count."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The lab report test type has a similar form to Complete Blood Count, albeit with a different phrasing."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 4.4, "unit": "10e6/microL"}, "conversion_details": null, "comment": "The unit in the lab test is identical to the reference unit, hence no conversion was necessary."}, "test_params": {"name": "RBC-Red Blood Cells", "result": 4.4, "range": "4-5.2", "units": "10^6/micl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 13, "result_value_type": "numeric_value"}}, {"match_data": {"id": 128, "test_type": "Complete Blood Count (CBC)", "parameter": "Neutrophils % (NEUT%)", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter 'Neutrophils %' is conceptually the same as 'Neutrophils % (NEUT%)'."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The test type is closely related to Complete Blood Count (CBC)."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 35.9, "unit": "%"}, "conversion_details": null, "comment": "Units are the same, no conversion needed."}, "test_params": {"name": "Neutrophils %", "result": 35.9, "range": "40-75", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 22, "result_value_type": "numeric_value"}}, {"match_data": {"id": 89, "test_type": "Coagulation panel", "parameter": "PT INR (INR)", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter name in the lab report matches the database with minor formatting differences (abbreviated form)."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The lab test type in the report corresponds closely to the matched reference test type with minor differences in language."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 1, "unit": "INR"}, "conversion_details": null, "comment": "No conversion needed, result is already in standard unit."}, "test_params": {"name": "I.N.R", "result": 1, "range": null, "units": null, "test_type": "קרישה", "comment": "נבדקים עם תסמונת אנטיפוספוליפידית (Antiphospholipid syndrome) עלולה להיות הארכה של תוצאות PT (Seconds) ו-INR.", "comment_english": "Patients with Antiphospholipid syndrome may have prolonged PT (Seconds) and INR results.", "index": 33, "result_value_type": "numeric_value"}}, {"match_data": {"id": 126, "test_type": "Complete Blood Count (CBC)", "parameter": "Monocytes No. (MONO#)", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "Parameter names match conceptually despite minor differences in formatting."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "Test type names have minor differences in formatting."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 0.45, "unit": "10e3/microL"}, "conversion_details": null, "comment": "No conversion necessary as the units match the reference."}, "test_params": {"name": "Monocytes #", "result": 0.45, "range": "0-1.1", "units": "10^3/micl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 29, "result_value_type": "numeric_value"}}, {"match_data": {"id": 418, "test_type": "Metabolic Panel", "parameter": "Iron (Fe) - blood", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The laboratory parameter name 'Fe - Iron' closely matches 'Iron (Fe) - blood', with minor variations."}, "match_test_type_info": {"match_score": "Unknown", "reason": "The test type 'כימיה בדם' does not match exactly with any test types in the index."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 88, "unit": "microg/dL"}, "conversion_details": null, "comment": "The unit is the same as the reference unit, no conversion needed."}, "test_params": {"name": "Fe - Iron", "result": 88, "range": "50-170", "units": "micg/dl", "test_type": "כימיה בדם", "comment": "מיום 22.3.23 חל שינוי בערכת הבדיקה. שים לב לשינוי בערכי הייחוס.", "comment_english": "From 22.3.23 there is a change in the test kit. Pay attention to the change in reference values.", "index": 9, "result_value_type": "numeric_value"}}, {"match_data": {"id": 352, "test_type": "Metabolic Panel", "parameter": "Asparatate aminotransferase, AST (GOT) - blood", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter name 'AST (GOT)' matches closely with 'Asparatate aminotransferase, AST (GOT) - blood', though there may be case differences."}, "match_test_type_info": {"match_score": "Unknown", "reason": "The test type 'Chemistry in Blood' does not match any defined test type, therefore it is classified as unknown."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 17, "unit": "U/L"}, "conversion_details": null, "comment": "No conversion needed as the units match the reference."}, "test_params": {"name": "AST (GOT)", "result": 17, "range": "10-31", "units": "U/l", "test_type": "כימיה בדם", "comment": "תרום 20.1.23 חל שינוי בערכת הבדיקה", "comment_english": "From 20.1.23 there is a change in the test kit", "index": 8, "result_value_type": "numeric_value"}}, {"match_data": {"id": 118, "test_type": "Complete Blood Count (CBC)", "parameter": "Hemoglobin (Hb)", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter name 'Hemoglobin' matches with 'Hemoglobin (Hb)' with minor formatting differences."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The test type 'Hematology - Complete Blood Count' is similar to 'Complete Blood Count (CBC)' with minor variations in wording."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 13.8, "unit": "g/dL"}, "conversion_details": {"factor": null, "calculation": null}, "comment": "Unit is already in the reference unit."}, "test_params": {"name": "Hemoglobin", "result": 13.8, "range": "12.5-16", "units": "g/dl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 14, "result_value_type": "numeric_value"}}, {"match_data": {"id": 397, "test_type": "Metabolic Panel", "parameter": "Glucose (Glu) - blood", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter name 'Glucose (B)' conceptually matches with 'Glucose (Glu) - blood' with minor formatting differences."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The test type uses a different term, but the parameter names were matched first."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 81, "unit": "mg/dL"}, "conversion_details": null, "comment": "No conversion needed as the units are the same."}, "test_params": {"name": "Glucose (B)", "result": 81, "range": "70-100", "units": "mg/dl", "test_type": "כימיה בדם", "comment": "מיום 20.1.23 חל שינוי בערכת הבדיקה", "comment_english": "From 20.1.23 there is a change in the test kit", "index": 0, "result_value_type": "numeric_value"}}, {"match_data": {"id": 91, "test_type": "Coagulation panel", "parameter": "PT sec (PT)", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter 'PT (Seconds)' matches 'PT sec (PT)' conceptually with minor variations."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The test type 'Coagulation' closely relates to 'Coagulation panel'."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 10.3, "unit": "Sec"}, "conversion_details": null, "comment": "No conversion needed as the units match the reference."}, "test_params": {"name": "PT (Seconds)", "result": 10.3, "range": "9.5-11.3", "units": "Sec.", "test_type": "קרישה", "comment": null, "comment_english": null, "index": 32, "result_value_type": "numeric_value"}}, {"match_data": {"id": 120, "test_type": "Complete Blood Count (CBC)", "parameter": "Lymphocytes No. (LYM#)", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "Parameter names match conceptually, slight formatting difference."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "Test type is closely related with minor case differences."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 3.36, "unit": "10e3/microL"}, "conversion_details": {"factor": null, "calculation": null}, "comment": "No conversion needed, units match reference."}, "test_params": {"name": "Lymphocytes #", "result": 3.36, "range": "1-4.8", "units": "10^3/micl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 28, "result_value_type": "numeric_value"}}, {"match_data": {"id": 119, "test_type": "Complete Blood Count (CBC)", "parameter": "Lymphocytes % (LYM%)", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The lab report's parameter matches the index parameter."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The test type in the lab report conceptually matches the index test type."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 53.7, "unit": "%"}, "conversion_details": null, "comment": "The result is already in the reference unit."}, "test_params": {"name": "Lymphocytes %", "result": 53.7, "range": "22-43", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 23, "result_value_type": "numeric_value"}}, {"match_data": {"id": 122, "test_type": "Complete Blood Count (CBC)", "parameter": "MCHC", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "Parameter MCHC matches perfectly."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "Test types are conceptually similar."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 34.4, "unit": "g/dL"}, "conversion_details": null, "comment": "No conversion necessary as units match."}, "test_params": {"name": "MCHC-M.Cell Hb cont.", "result": 34.4, "range": "31-36", "units": "g/dl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 18, "result_value_type": "numeric_value"}}, {"match_data": {"id": 129, "test_type": "Complete Blood Count (CBC)", "parameter": "Neutrophils No. (NEUT#)", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Alternative", "reason": "The parameter name matches conceptually with a different but medically equivalent term, and the test type aligns with a related name."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The test type matches conceptually with minor variations."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 2.25, "unit": "10e3/microL"}, "conversion_details": {"factor": null, "calculation": null}, "comment": "No conversion needed as it is already in the reference unit."}, "test_params": {"name": "Neutrophils #", "result": 2.25, "range": "1.8-7.7", "units": "10^3/micl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 27, "result_value_type": "numeric_value"}}, {"match_data": {"id": 375, "test_type": "Metabolic Panel", "parameter": "Creatinine (Cr) - blood", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter name 'Creatinine (B)' matches closely with the existing parameter 'Creatinine (Cr) - blood'."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The test type 'Blood Chemistry' corresponds to the 'Metabolic Panel', indicating a suitable match."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 0.74, "unit": "mg/dL"}, "conversion_details": {"factor": null, "calculation": null}, "comment": "Result unit is already in the required reference unit."}, "test_params": {"name": "<PERSON><PERSON>tinine (B)", "result": 0.74, "range": "0.5-1", "units": "mg/dl", "test_type": "כימיה בדם", "comment": "מיום 20.1.23 חל שינוי בערכת הבדיקה. שים לב לשינוי בערכי הייחוס", "comment_english": "From 20.1.23 there is a change in the test kit. Pay attention to the change in reference values", "index": 2, "result_value_type": "numeric_value"}}, {"match_data": {"id": 463, "test_type": "Metabolic Panel", "parameter": "Urea nitrogen (BUN) - blood", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Alternative", "reason": "The parameter Urea in the report matches Urea nitrogen (BUN) in the reference data, though under different terminology."}, "match_test_type_info": {"match_score": "Alternative", "reason": "The test type Blood Chemistry relates closely to the Metabolic Panel."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 31, "unit": "mg/dL"}, "conversion_details": null, "comment": "The current units are equivalent to the reference units, no conversion needed."}, "test_params": {"name": "Urea (B)", "result": 31, "range": "19-49", "units": "mg/dl", "test_type": "כימיה בדם", "comment": "מיום 20.1.23 חל שינוי בערכת הבדיקה. שים לב לשינוי בערכים המודדים וערכי הייחוס", "comment_english": "From 20.1.23 there is a change in the test kit. Pay attention to the change in measured values and reference values", "index": 1, "result_value_type": "numeric_value"}}, {"match_data": {"id": 223, "test_type": "Endocrinology", "parameter": "Thyroid stimulating hormone (TSH) - blood", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter name matches conceptually but there was a slight case difference in the units (miu/l vs mu/L)."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "Test type in the report (אנדו<PERSON>רינולוגיה) matches the test type (Endocrinology) in the database with a language mismatch."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 2.13, "unit": "mu/L"}, "conversion_details": null, "comment": "The result unit is the same as the reference unit."}, "test_params": {"name": "TSH", "result": 2.13, "range": "0.5-4.8", "units": "miu/l", "test_type": "אנדוקרינולוגיה", "comment": "החל מתאריך 18.9.22 חל שינוי בערכת הבדיקה. שים לב לערכון ערכי הייחוס.", "comment_english": "From 18.9.22 there is a change in the test kit. Pay attention to the update in reference values.", "index": 35, "result_value_type": "numeric_value"}}, {"match_data": {"id": 114, "test_type": "Complete Blood Count (CBC)", "parameter": "Basophils No. (BASO#)", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter name 'Basophils #' matches with 'Basophils No. (BASO#)' with a slight variation in formatting."}, "match_test_type_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The test type 'המטולוגיה- ספירת דם - מכ<PERSON><PERSON><PERSON>' translates to 'Hematology - Complete Blood Count - Device', which is similar to 'Complete Blood Count (CBC)' with variations in the naming."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 0.03, "unit": "10e3/microL"}, "conversion_details": {"factor": null, "calculation": null}, "comment": "The original units '10^3/micl' (interpreted as '10e3/microL') matched the reference unit."}, "test_params": {"name": "Basophils #", "result": 0.03, "range": "0-0.2", "units": "10^3/micl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 31, "result_value_type": "numeric_value"}}, {"match_data": {"id": 339, "test_type": "Metabolic Panel", "parameter": "Alkaline Phosphatase (Alk Phos) - blood", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter name matches conceptually, with minor differences in formatting."}, "match_test_type_info": {"match_score": "Unknown", "reason": "The test type provided does not exactly match the existing test types."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 57, "unit": "U/L"}, "conversion_details": null, "comment": "No conversion needed as the unit is the same as the reference."}, "test_params": {"name": "ALKP-Alkaline Phosphatase", "result": 57, "range": "30-120", "units": "u/l", "test_type": "כימיה בדם", "comment": "מיום 20.1.23 חל שינוי בערכת הבדיקה", "comment_english": "From 20.1.23 there is a change in the test kit", "index": 6, "result_value_type": "numeric_value"}}, {"match_data": {"id": 121, "test_type": "Complete Blood Count (CBC)", "parameter": "MCH", "sample_type": "Blood"}, "match_parameter_info": {"match_score": "Similar: <PERSON><PERSON>", "reason": "The parameter name 'MCH' matches with the report parameter 'MCH-Mean Cell Hemoglobin' with minor variations."}, "match_test_type_info": {"match_score": "Alternative", "reason": "The test type 'Hematology - Blood Count - Instrument' is related to 'Complete Blood Count (CBC).' However, this match is secondary to the parameter match."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 31.4, "unit": "pg"}, "conversion_details": null, "comment": "The original unit 'pg/cell' could not be converted to the required unit but matches in the context."}, "test_params": {"name": "MCH-Mean Cell Hemoglobin", "result": 31.4, "range": "27-34", "units": "pg/cell", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 17, "result_value_type": "numeric_value"}}]}, "analysis": {"stage": "lab_report_matching", "message": "Successfully matched all lab reports"}}