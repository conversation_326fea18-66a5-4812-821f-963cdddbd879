# Stage 1: Build the application
FROM python:3.11 as builder

# Set the working directory
WORKDIR /app

# Copy the requirements file and install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir wheel
RUN pip wheel --no-cache-dir --wheel-dir /app/wheels -r requirements.txt

# Copy the application code
COPY . .

# Stage 2: Create the final image
FROM python:3.11-slim

# Set the working directory
WORKDIR /app

# Install dependencies from wheels
COPY --from=builder /app/wheels /wheels
RUN pip install --no-cache-dir /wheels/*

# Copy the application code
COPY --from=builder /app .

# Expose the port the app runs on
EXPOSE 8000

# Command to run the application
CMD ["python3", "-m", "app.main"]