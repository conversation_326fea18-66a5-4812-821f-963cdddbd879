# mediboard-ocr-llm-service

## Project Setup



### Create a Virtual Environment (env)
Setup a local environment using: 
```bash
python -m venv env
```

### Activate the Virtual Environment

On Windows run: 
```bash
env\\Scripts\\activate
```

On macOS/Linux run: 
```bash
source env/bin/activate
```

## Running the API Server

Installation of dependencies is done using setup.py. Run the following command to install the dependencies:
```bash
cd '/path/to/proj-jesse-ai-agent'
```
For development use:
```bash
pip install -e .
```
For production use:
```bash
pip install .
```


Finally, run this script to have the server running: 
For development use:
```bash
uvicorn app.main:app --reload
```
For production use:
```bash
python .\api\main.py
```

# Redis Development Setup

## Prerequisites
- Ensure you have [Redis](https://redis.io/download) installed on your machine.

## Running Redis in Development Mode

1. **Start Redis Server:**
   Open your terminal and run the following command to start the Redis server:
   ```bash
   redis-server
   ```

1. **Stop Redis Server:**
   To stop the Redis server, simply press ```Ctrl+C``` in the terminal where the server is running


## Important!

Install the dependencies:
```bash
pip install -r requirements.txt
```

For dependency updates, please run the following command whenever a new dependency is added:
```bash
pip freeze > requirements.txt
```
This will update the \`requirements.txt\` for easy setup in a different working environment.

Done!

## For working in a container environment (Docker)

1. Install Docker.

2. Install Docker Compose.

3. To enable faster startup times, install the following Docker images:
```bash
docker pull python:3.11
docker pull redis:5.0-bullseye
```

4. Start up the development environment with this command
```bash
docker-compose -f docker-compose-dev.yml up --build
```

5. Remove all the containers in the development environment with this command:
```bash
docker-compose -f docker-compose-dev.yml down
```

