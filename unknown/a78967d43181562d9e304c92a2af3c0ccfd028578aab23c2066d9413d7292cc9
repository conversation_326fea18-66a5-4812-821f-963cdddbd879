events {
  worker_connections 1024;
}

http {
  client_max_body_size 10M;
  upstream app_servers {
    server app:8000;
  }

  server {
    listen 80;
    server_name localhost;

    location / {
      proxy_pass http://app_servers;
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_read_timeout 365d;
      proxy_send_timeout 365d;
      proxy_connect_timeout 365d;
    }

    location /status {
      stub_status;
      allow all; # Open for all in this example. Restrict in production.
    }

    client_body_timeout 365d;
    client_header_timeout 365d;
    keepalive_timeout 365d;
  }
}