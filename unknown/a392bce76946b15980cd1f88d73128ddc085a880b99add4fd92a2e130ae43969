import logging
import sys
from typing import Any
from .error_handler import get_error_details

class Logger:
    def __init__(self, name: str = __name__):
        # Configure the logger
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.INFO)

        # Create console handler with formatting
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(formatter)
        
        # Add handler to logger
        self.logger.addHandler(console_handler)

    def info(self, message: str, *args: Any, **kwargs: Any) -> None:
        """Log an info level message"""
        self.logger.info(message, *args, **kwargs)

    def warning(self, message: str, *args: Any, **kwargs: Any) -> None:
        """Log a warning level message"""
        self.logger.warning(message, *args, **kwargs)

    def error(self, message: str, *args: Any, **kwargs: Any) -> None:
        """Log an error level message"""
        self.logger.error(message, *args, **kwargs)

    def debug(self, message: str, *args: Any, **kwargs: Any) -> None:
        """Log a debug level message"""
        self.logger.debug(message, *args, **kwargs)

    def critical(self, message: str, *args: Any, **kwargs: Any) -> None:
        """Log a critical level message"""
        self.logger.critical(message, *args, **kwargs)

    def exception(self, error: Exception) -> None:
        """
        Log an exception with detailed error information
        using the error handler
        """
        error_details = get_error_details(error)
        self.error(
            f"🚨 Exception caught:\n"
            f"Type: {error_details['error_type']}\n"
            f"Message: {error_details['message']}\n" 
            f"File: {error_details['file_name']}\n"
            f"Line: {error_details['line_number']}\n"
            f"Function: {error_details['function_name']}"
        )

# Create global logger instance
logger = Logger("app")
