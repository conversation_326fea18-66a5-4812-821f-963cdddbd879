import os
from typing import Any, Dict, Optional
from fastapi import APIRouter
from app.core.config import settings
from pydantic import BaseModel, Field

check_router = APIRouter(
    prefix="/check", responses={404: {"description": "Not found"}}, tags=["check"]
)

class ConfigResponse(BaseModel):
    OPENAI_API_KEY: Optional[str] = Field(None, description="OpenAI API Key")
    LANGFUSE_SECRET_KEY: Optional[str] = Field(None, description="Langfuse Secret Key")
    LANGFUSE_PUBLIC_KEY: Optional[str] = Field(None, description="Langfuse Public Key")
    LANGFUSE_HOST: Optional[str] = Field(None, description="Langfuse Host")
    CLAUDE_API_KEY: Optional[str] = Field(None, description="Claude API Key")
    LLAMA_CLOUD_API_KEY: Optional[str] = Field(None, description="Llama Cloud API Key")
    NOMIC_API_KEY: Optional[str] = Field(None, description="Nomic API Key")
    REDIS_HOST: Optional[str] = Field(None, description="Redis Host")
    REDIS_PORT: Optional[int] = Field(None, description="Redis Port")
    REDIS_DB: Optional[int] = Field(None, description="Redis DB")
    SLACK_TOKEN: Optional[str] = Field(None, description="Slack Token")
    GROQ_API_KEY: Optional[str] = Field(None, description="Groq API Key")

@check_router.get("/config", response_model=ConfigResponse)
async def get_config() -> ConfigResponse:
    """
    Returns the configuration data as JSON.
    """
    return settings
