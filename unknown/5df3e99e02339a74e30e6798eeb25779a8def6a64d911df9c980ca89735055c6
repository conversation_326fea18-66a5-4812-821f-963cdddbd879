from pydantic import ValidationError
from pydantic import BaseModel, Field

class DataProcessor:
    @staticmethod
    def validate_data_schema(data: dict, model: BaseModel) -> bool:
        """
        Validates the given data against the provided Pydantic model schema.

        Args:
            data (dict): The data to validate.
            model (BaseModel): The Pydantic model to validate against.

        Returns:
            bool: True if the data is valid according to the schema, False otherwise.
        """
        try:
            model(**data)
            return True
        except ValidationError as e:
            print(f"Validation error: {e}")
            return False
