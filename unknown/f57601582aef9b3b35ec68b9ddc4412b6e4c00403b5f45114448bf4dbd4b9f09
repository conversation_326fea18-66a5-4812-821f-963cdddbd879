global:
  resolve_timeout: 1m
  slack_api_url: '*********************************************************************************'

route:
  group_by: 
    - 'alertname'
    - 'severity'
  group_wait: 30s
  group_interval: 5m
  repeat_interval: 4h
  receiver: 'slack-notifications'
  routes:
    - receiver: 'slack-notifications-resolution'
      group_wait: 0s
      group_interval: 1m
      repeat_interval: 12h
      matchers:
        - alertname=~".*"
      continue: true
      mute_time_intervals:
        - mute-repeated-resolutions

receivers:
  - name: 'slack-notifications'
    slack_configs:
      - channel: '#mediboard-notifications'
        send_resolved: true
        icon_emoji: ':warning:'
        title: '[{{ .Status | toUpper }}{{ if eq .Status "firing" }}:{{ .Alerts.Firing | len }}{{ end }}] {{ .CommonLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Severity:* {{ .Labels.severity }}
          *Started:* {{ .StartsAt | toString }}
          {{ if ne .Status "firing" }}
          *Resolved:* {{ .EndsAt | toString }}
          {{ end }}
          ---
          {{ end }}

  - name: 'slack-notifications-resolution'
    slack_configs:
      - channel: '#mediboard-notifications'
        send_resolved: true
        icon_emoji: ':white_check_mark:'
        title: '🎉 [RESOLVED] Systems back to normal'
        text: |
          {{ range .Alerts }}
          *Resolved Alert:* {{ .Annotations.summary }}
          *Description:* System has returned to normal operating parameters
          *Previously Started:* {{ .StartsAt | toString }}
          *Resolved At:* {{ .EndsAt | toString }}
          ---
          {{ end }}

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname']

time_intervals:
  - name: mute-repeated-resolutions
    time_intervals:
      - times:
          - start_time: '00:00'
            end_time: '23:59'
