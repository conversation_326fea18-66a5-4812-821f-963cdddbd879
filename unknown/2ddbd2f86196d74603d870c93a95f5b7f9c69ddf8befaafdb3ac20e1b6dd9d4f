# Systemd unit file for LLM service attached to Mediboard

[Unit]
# Human-readable name for the service
Description=Mediboard LLM Service

# Tell the service to start after the system boots up the kernel
After=multi-user.target

[Service]
# The working directory to run all commands from
WorkingDirectory=/home/<USER>/llm

# Command to execute when this systemd service starts up
ExecStart=/usr/bin/python3 /home/<USER>/llm/app/main.py

# Disable Python's buffering of STDOUT and STDERR, so that output from the
# service shows up immediately in systemd's logs
Environment=PYTHONUNBUFFERED=1

# Automatically restart the service if it crashes
Restart=on-failure

# The service will notify systemd once it is up and running
Type=notify

# Use a dedicated user to run our service
User=ec2-user

# specifying logging to be used
StandardOuput=append:/var/log/mdb-llm.log
StandardError=append:/var/log/mdb-llm.log

[Install]
WantedBy=multi-user.target
