services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "127.0.0.1:8000:8000"
    env_file:
      - .env
    networks:
      - 'app-network'

  redis:
    image: redis:5.0-bullseye
    depends_on:
      app:
        condition: service_started
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: '30s'
      timeout: '5s'
      retries: 3
      start_period: '10s'
    networks:
      - 'app-network'

networks:
  app-network:
    driver: bridge