{"__inputs": [{"name": "DS_PROM", "label": "prom", "description": "", "type": "datasource", "pluginId": "prometheus", "pluginName": "Prometheus"}], "__elements": {}, "__requires": [{"type": "panel", "id": "gauge", "name": "Gauge", "version": ""}, {"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "10.3.3"}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}, {"type": "panel", "id": "stat", "name": "Stat", "version": ""}, {"type": "panel", "id": "timeseries", "name": "Time series", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Redis Dashboard for Prometheus Redis Exporter 1.x", "editable": true, "fiscalYearStartMonth": 0, "gnetId": 763, "graphTooltip": 1, "id": null, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "${DS_PROM}"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "decimals": 0, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s", "unitScale": true}, "overrides": []}, "gridPos": {"h": 7, "w": 3, "x": 0, "y": 0}, "id": 9, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROM}"}, "expr": "max(max_over_time(redis_uptime_in_seconds{instance=~\"$instance\"}[$__interval]))", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "", "metric": "", "refId": "A", "step": 1800}], "title": "Max Uptime", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROM}"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "decimals": 0, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none", "unitScale": true}, "overrides": []}, "gridPos": {"h": 7, "w": 2, "x": 3, "y": 0}, "hideTimeOverride": true, "id": 12, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROM}"}, "expr": "sum(redis_connected_clients{instance=~\"$instance\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "metric": "", "refId": "A", "step": 2}], "timeFrom": "1m", "title": "Clients", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROM}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 0, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 80}, {"color": "rgba(245, 54, 54, 0.9)", "value": 95}]}, "unit": "percent", "unitScale": true}, "overrides": []}, "gridPos": {"h": 7, "w": 3, "x": 5, "y": 0}, "hideTimeOverride": true, "id": 11, "links": [], "maxDataPoints": 100, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto"}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROM}"}, "expr": "sum(100 * (redis_memory_used_bytes{instance=~\"$instance\"}  / redis_memory_max_bytes{instance=~\"$instance\"}))", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "metric": "", "refId": "A", "step": 2}], "timeFrom": "1m", "title": "Memory Usage", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROM}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short", "unitScale": true}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 0}, "id": 18, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROM}"}, "expr": "sum(rate(redis_commands_total{instance=~\"$instance\"} [1m])) by (cmd)", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{ cmd }}", "metric": "redis_command_calls_total", "refId": "A", "step": 240}], "title": "Total Commands / sec", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROM}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short", "unitScale": true}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 0}, "id": 1, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROM}"}, "expr": "irate(redis_keyspace_hits_total{instance=~\"$instance\"}[5m])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "hits, {{ instance }}", "metric": "", "refId": "A", "step": 240, "target": ""}, {"datasource": {"type": "prometheus", "uid": "${DS_PROM}"}, "expr": "irate(redis_keyspace_misses_total{instance=~\"$instance\"}[5m])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "misses, {{ instance }}", "metric": "", "refId": "B", "step": 240, "target": ""}], "title": "Hits / Misses per Sec", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROM}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes", "unitScale": true}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "max"}, "properties": [{"id": "color", "value": {"fixedColor": "#BF1B00", "mode": "fixed"}}]}]}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 7}, "id": 7, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROM}"}, "expr": "redis_memory_used_bytes{instance=~\"$instance\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "used, {{ instance }}", "metric": "", "refId": "A", "step": 240, "target": ""}, {"datasource": {"type": "prometheus", "uid": "${DS_PROM}"}, "expr": "redis_memory_max_bytes{instance=~\"$instance\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "max, {{ instance }}", "refId": "B", "step": 240}], "title": "Total Memory Usage", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROM}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes", "unitScale": true}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 7}, "id": 10, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROM}"}, "expr": "sum(rate(redis_net_input_bytes_total{instance=~\"$instance\"}[5m]))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{ input }}", "refId": "A", "step": 240}, {"datasource": {"type": "prometheus", "uid": "${DS_PROM}"}, "expr": "sum(rate(redis_net_output_bytes_total{instance=~\"$instance\"}[5m]))", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{ output }}", "refId": "B", "step": 240}], "title": "Network I/O", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROM}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 70, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none", "unitScale": true}, "overrides": [{"matcher": {"id": "byV<PERSON>ue", "options": {"op": "gte", "reducer": "allIsZero", "value": 0}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": true, "tooltip": true, "viz": false}}]}]}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 14}, "id": 5, "links": [], "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROM}"}, "expr": "sum (redis_db_keys{instance=~\"$instance\"}) by (db, instance)", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{ db }}, {{ instance }}", "refId": "A", "step": 240, "target": ""}], "title": "Total Items per DB", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROM}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 70, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short", "unitScale": true}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 14}, "id": 13, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROM}"}, "expr": "sum (redis_db_keys{instance=~\"$instance\"}) by (instance) - sum (redis_db_keys_expiring{instance=~\"$instance\"}) by (instance)", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "not expiring, {{ instance }}", "refId": "A", "step": 240, "target": ""}, {"datasource": {"type": "prometheus", "uid": "${DS_PROM}"}, "expr": "sum (redis_db_keys_expiring{instance=~\"$instance\"}) by (instance)", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "expiring, {{ instance }}", "metric": "", "refId": "B", "step": 240}], "title": "Expiring vs Not-Expiring Keys", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROM}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short", "unitScale": true}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "evicts"}, "properties": [{"id": "color", "value": {"fixedColor": "#890F02", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "memcached_items_evicted_total{instance=\"**********:9150\",job=\"prometheus\"}"}, "properties": [{"id": "color", "value": {"fixedColor": "#890F02", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "reclaims"}, "properties": [{"id": "color", "value": {"fixedColor": "#3F6833", "mode": "fixed"}}]}]}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 21}, "id": 8, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROM}"}, "expr": "sum(rate(redis_expired_keys_total{instance=~\"$instance\"}[5m])) by (instance)", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "expired, {{ instance }}", "metric": "", "refId": "A", "step": 240, "target": ""}, {"datasource": {"type": "prometheus", "uid": "${DS_PROM}"}, "expr": "sum(rate(redis_evicted_keys_total{instance=~\"$instance\"}[5m])) by (instance)", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "evicted, {{ instance }}", "refId": "B", "step": 240}], "title": "Expired/Evicted <PERSON>", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROM}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short", "unitScale": true}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 21}, "id": 16, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROM}"}, "expr": "sum(redis_connected_clients{instance=~\"$instance\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "connected", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROM}"}, "expr": "sum(redis_blocked_clients{instance=~\"$instance\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "blocked", "refId": "B"}], "title": "Connected/Blocked Clients", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROM}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s", "unitScale": true}, "overrides": [{"matcher": {"id": "byV<PERSON>ue", "options": {"op": "gte", "reducer": "allIsZero", "value": 0}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": true, "tooltip": true, "viz": false}}]}]}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 28}, "id": 20, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROM}"}, "expr": "sum(irate(redis_commands_duration_seconds_total{instance =~ \"$instance\"}[1m])) by (cmd)\n  /\nsum(irate(redis_commands_total{instance =~ \"$instance\"}[1m])) by (cmd)\n", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{ cmd }}", "metric": "redis_command_calls_total", "refId": "A", "step": 240}], "title": "Average Time Spent by Command / sec", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROM}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s", "unitScale": true}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 28}, "id": 14, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROM}"}, "expr": "sum(irate(redis_commands_duration_seconds_total{instance=~\"$instance\"}[1m])) by (cmd) != 0", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{ cmd }}", "metric": "redis_command_calls_total", "refId": "A", "step": 240}], "title": "Total Time Spent by Command / sec", "type": "timeseries"}], "refresh": "", "schemaVersion": 39, "tags": ["prometheus", "redis"], "templating": {"list": [{"current": {}, "datasource": {"type": "prometheus", "uid": "${DS_PROM}"}, "definition": "label_values(redis_up, namespace)", "hide": 0, "includeAll": false, "multi": false, "name": "namespace", "options": [], "query": "label_values(redis_up, namespace)", "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"current": {}, "datasource": {"type": "prometheus", "uid": "${DS_PROM}"}, "definition": "label_values(redis_up{namespace=~\"$namespace\"}, instance)", "hide": 0, "includeAll": false, "multi": true, "name": "instance", "options": [], "query": "label_values(redis_up{namespace=~\"$namespace\"}, instance)", "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-24h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "Redis Dashboard for Prometheus Redis Exporter 1.x", "uid": "e008bc3f-81a2-40f9-baf2-a33fd8dec7ec", "version": 2, "weekStart": ""}