import logging
from langfuse import Lang<PERSON>
from app.core.config import settings
from langfuse.callback import CallbackHandler

logger = logging.getLogger(__name__)

class LangfuseManager:
    def __init__(self, config=None):
        """
        Initializes the Langfuse SDK with the provided configuration.

        :param config: Dictionary containing 'langfuse-secret-key', 'langfuse-public-key', and 'langfuse-host'.
        """

        def ensure_protocol(host_url):
            # Check if the host URL starts with 'http://' or 'https://'
            if not host_url.startswith(('http://', 'https://')):
                return f"http://{host_url}"  # Default to 'http://' if protocol is missing
            return host_url

        # print("Langfuse host: ", settings.LANGFUSE_HOST)

        # Ensure host URL has the correct protocol
        host_url = (
            ensure_protocol(config.get("langfuse-host", settings.LANGFUSE_HOST))
            if config
            else ensure_protocol(settings.LANGFUSE_HOST)
        )

        # Initialize Langfuse with the modified host URL
        self.langfuse = Langfuse(
            secret_key=(
                config.get("langfuse-secret-key", settings.LANGFUSE_SECRET_KEY)
                if config
                else settings.LANGFUSE_SECRET_KEY
            ),
            public_key=(
                config.get("langfuse-public-key", settings.LANGFUSE_PUBLIC_KEY)
                if config
                else settings.LANGFUSE_PUBLIC_KEY
            ),
            host=host_url,  # Use the protocol-ensured host URL
        )

        if not self.langfuse:
            raise ValueError("Failed to initialize Langfuse SDK. Please check your configuration.")
        
    def get_prompt(self, prompt_name, version=1):
        """
        Retrieves a prompt from Langfuse.

        :param prompt_name: The name of the prompt to retrieve.
        :param version: The version of the prompt (default is 1).
        :return: The retrieved prompt.
        """
        logger.info(f"Attempting to retrieve prompt: {prompt_name}, version: {version}")
        try:
            prompt = self.langfuse.get_prompt(prompt_name, version=version)
            logger.info(f"Successfully retrieved prompt: {prompt_name}, version: {version}")
            return prompt
        except Exception as e:
            logger.error(f"Failed to retrieve prompt: {prompt_name}, version: {version}. Error: {e}")
            raise

    def callback_handler(self, additional_params=None):
        """
        Initializes and returns a CallbackHandler with default and additional parameters.

        :param additional_params: Dictionary of additional parameters to append (default is None).
        :return: An instance of CallbackHandler with combined parameters.
        """
        # Default parameters for CallbackHandler
        default_params = {
            "secret_key": settings.LANGFUSE_SECRET_KEY,
            "public_key": settings.LANGFUSE_PUBLIC_KEY,
            "host": settings.LANGFUSE_HOST,
        }

        if additional_params:
            # Merge additional parameters with the default parameters
            default_params.update(additional_params)

        # Create and return the CallbackHandler instance
        return CallbackHandler(**default_params)


# # Example usage
# if __name__ == "__main__":
#     # Configuration dictionary
#     config = {
#         'langfuse-secret-key': 'your-secret-key',
#         'langfuse-public-key': 'your-public-key',
#         'langfuse-host': 'your-langfuse-host'
#     }

#     # Initialize LangfuseManager
#     langfuse_manager = LangfuseManager(config)

#     # Query prompt
#     sop_prompt = langfuse_manager.get_prompt("sop-step-1-table", version=2)
#     print("Retrieved Prompt:", sop_prompt)

#     # Handle callback with additional parameters
#     additional_params = {
#         "custom_param_1": "value_1",
#         "custom_param_2": "value_2"
#     }
#     sop_callback_handler = langfuse_manager.callback_handler(additional_params)
#     print("CallbackHandler Initialized:", sop_callback_handler)
