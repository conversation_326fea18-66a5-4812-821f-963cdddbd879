{"__inputs": [{"name": "DS_PROMETHEUS", "label": "Prometheus", "description": "", "type": "datasource", "pluginId": "prometheus", "pluginName": "Prometheus"}], "__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "5.0.0"}, {"type": "panel", "id": "graph", "name": "Graph", "version": ""}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}, {"type": "panel", "id": "singlestat", "name": "Singlestat", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Official dashboard for NGINX Prometheus exporter\r\n", "editable": true, "gnetId": 11199, "graphTooltip": 0, "id": null, "iteration": 1562682051068, "links": [], "panels": [{"datasource": "${DS_PROMETHEUS}", "collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 4, "panels": [], "title": "Status", "type": "row"}, {"datasource": "${DS_PROMETHEUS}", "cacheTimeout": null, "colorBackground": true, "colorPostfix": false, "colorPrefix": false, "colorValue": false, "colors": ["#E02F44", "#FF9830", "#299c46"], "decimals": null, "description": "", "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 3, "w": 12, "x": 0, "y": 1}, "id": 8, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "options": {}, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeat": "instance", "repeatDirection": "h", "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "nginx_up{instance=~\"$instance\"}", "format": "time_series", "instant": false, "intervalFactor": 1, "refId": "A"}], "thresholds": "1,1", "timeFrom": null, "timeShift": null, "title": "NGINX Status for $instance", "type": "singlestat", "valueFontSize": "100%", "valueMaps": [{"op": "=", "text": "Down", "value": "0"}, {"op": "=", "text": "Up", "value": "1"}], "valueName": "current"}, {"datasource": "${DS_PROMETHEUS}", "collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 4}, "id": 6, "panels": [], "title": "Metrics", "type": "row"}, {"datasource": "${DS_PROMETHEUS}", "aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "decimals": null, "description": "", "fill": 1, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 5}, "id": 10, "legend": {"alignAsTable": false, "avg": false, "current": false, "hideEmpty": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(nginx_connections_accepted{instance=~\"$instance\"}[5m])", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{instance}} accepted", "refId": "A"}, {"expr": "irate(nginx_connections_handled{instance=~\"$instance\"}[5m])", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{instance}} handled", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Processed connections", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": 1, "format": "short", "label": "Connections (rate)", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": "", "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": "${DS_PROMETHEUS}", "aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "decimals": 0, "fill": 1, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 5}, "id": 12, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "nginx_connections_active{instance=~\"$instance\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}} active", "refId": "A"}, {"expr": "nginx_connections_reading{instance=~\"$instance\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}} reading", "refId": "B"}, {"expr": "nginx_connections_waiting{instance=~\"$instance\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}} waiting", "refId": "C"}, {"expr": "nginx_connections_writing{instance=~\"$instance\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}} writing", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Active Connections", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": 0, "format": "short", "label": "Connections", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": "${DS_PROMETHEUS}", "aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "fill": 1, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 15}, "id": 15, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(nginx_http_requests_total{instance=~\"$instance\"}[5m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}} total requests", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Total requests", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": "5s", "schemaVersion": 18, "style": "dark", "tags": ["nginx", "prometheus", "nginx prometheus exporter"], "templating": {"list": [{"allValue": null, "current": {}, "datasource": "${DS_PROMETHEUS}", "definition": "label_values(nginx_up, instance)", "hide": 0, "includeAll": true, "label": "", "multi": true, "name": "instance", "options": [], "query": "label_values(nginx_up, instance)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-15m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "NGINX by nginxinc", "uid": "MsjffzSZz", "version": 1}