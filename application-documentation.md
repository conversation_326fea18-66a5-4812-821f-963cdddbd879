# Mediboard-LLM Application Documentation

## Overview

Mediboard-LLM is a specialized application designed to process and analyze medical lab test reports using Optical Character Recognition (OCR) and Large Language Models (LLMs). The system extracts structured data from uploaded medical documents, identifies key information such as patient details, physician information, medical facility data, and lab test results, and matches this information against existing databases.

The application serves as a bridge between unstructured medical documents and structured data systems, enabling healthcare providers to efficiently digitize and process lab test reports.

## Core Functionality

### 1. Document Processing and OCR

The application accepts uploaded PDF documents (typically lab test reports) and processes them through the following steps:

1. **Document Upload**: Users upload PDF files through the `/ocr/process` endpoint, providing a test ID, user ID, and language preference.
2. **Document Parsing**: The system uses LlamaParse (via the LlamaIndex API) to extract text content from the document.
3. **Structured Data Extraction**: LLMs (Claude, GPT-4, or Groq) analyze the extracted text to identify and structure key information.
4. **Data Validation**: The extracted data is validated against predefined Pydantic models to ensure consistency and completeness.

### 2. Entity Matching

After extracting structured data, the system matches the identified entities against existing databases:

1. **Physician Matching**: Matches the extracted physician information with a database of known physicians.
2. **Medical Facility Matching**: Matches the identified medical facility with a database of known institutions.
3. **Lab Test Matching**: Matches extracted lab tests with standardized lab test parameters.

### 3. Session Management and Progress Tracking

The application uses Redis to manage processing sessions and track progress:

1. **Session Creation**: Each document processing request creates a unique session identified by the test ID.
2. **Stage Tracking**: The processing is divided into stages (document summary, physician matching, institution matching, lab report matching), each with its own progress and status.
3. **Webhook Notifications**: The system sends webhook notifications at various stages to inform external systems about the processing status.

## System Architecture

### Components

1. **FastAPI Backend**: Handles HTTP requests, manages background tasks, and coordinates the processing workflow.
2. **LLM Services**: Integrates with various LLM providers (OpenAI, Anthropic, Groq) for text analysis and extraction.
3. **LlamaIndex Integration**: Connects to LlamaParse for document parsing and OCR.
4. **Redis Session Store**: Maintains processing state and enables progress tracking.
5. **Vector Database**: Stores and retrieves vectorized data for entity matching.
6. **Webhook Service**: Sends notifications to external systems about processing status.
7. **Task Manager**: Handles parallel processing of multiple lab reports.

### Data Flow

1. **Input**: PDF document uploaded via API
2. **Processing**:
   - Document parsing with LlamaParse
   - Structured data extraction with LLMs
   - Entity matching against databases
3. **Output**: Structured JSON data containing:
   - Patient information
   - Physician information (with matching results)
   - Medical facility information (with matching results)
   - Lab test results (with matching results)

## Detailed Workflow

### 1. Document Upload and Initial Processing

When a document is uploaded to the `/ocr/process` endpoint:

1. The file is saved temporarily to the server.
2. A Redis session is created with the provided test ID.
3. A background task is initiated to process the document.
4. The API returns immediately with a success message and the test ID.

### 2. Document Summary Generation

The background processing task:

1. Updates the session status to indicate processing has started.
2. Uploads the document to LlamaParse via the LlamaIndex API.
3. Retrieves the parsed markdown content from LlamaParse.
4. Uses an LLM chain (with primary and fallback models) to extract structured data.
5. Validates the extracted data against the `UploadedFileContent` model.
6. Updates the Redis session with the extracted data.
7. Checks if the document is a lab report; if not, processing stops.

### 3. Entity Matching Process

If the document is a lab report, the system proceeds with entity matching:

#### Physician Matching

1. Extracts physician information from the document summary.
2. Updates the session status to indicate physician matching has started.
3. Sends a webhook notification about the start of physician matching.
4. Uses vector search to find the best match for the physician in the database.
5. Updates the session with the matched physician information.
6. Sends a webhook notification about the completion of physician matching.

#### Medical Facility Matching

1. Extracts medical facility information from the document summary.
2. Updates the session status to indicate institution matching has started.
3. Sends a webhook notification about the start of institution matching.
4. Uses vector search to find the best match for the medical facility in the database.
5. Updates the session with the matched institution information.
6. Sends a webhook notification about the completion of institution matching.

#### Lab Report Matching

1. Extracts lab report information from the document summary.
2. Updates the session status to indicate lab report matching has started.
3. Sends a webhook notification about the start of lab report matching.
4. For each lab test parameter:
   - Creates a thread to process the lab test in parallel.
   - Uses vector search to find the best match for the lab test in the database.
   - Adds the matched lab test to the results.
5. Waits for all threads to complete.
6. Updates the session with the matched lab report information.
7. Sends a webhook notification about the completion of lab report matching.

### 4. Process Completion

1. Sends a webhook notification about the completion of the entire process.
2. Returns the processed data, including all matched entities.
3. Cleans up temporary files.

## Models and Data Structures

### Key Pydantic Models

1. **UploadedFileContent**: The main model for extracted document data, containing:
   - `patient_info`: Patient details (first name, last name)
   - `physician_info`: Physician details (first name, last name)
   - `medical_facility`: Medical facility details (name, location)
   - `is_lab_report`: Boolean indicating if the document is a lab report
   - `test_date`: Date of the lab test
   - `lab_reports`: List of lab test parameters

2. **LabTestParameter**: Model for individual lab test results, containing:
   - `name`: Test parameter name
   - `result`: Test result value
   - `range`: Reference range
   - `units`: Measurement units
   - `test_type`: Type of test
   - `comment`: Additional comments from a doctor
   - `comment_english`: English translation of comments (if in Hebrew language)
   - `result_value_type`: Type of result value (numeric, positive/negative, etc.)

3. **Processing Stage Models**:
   - `StageDataSummary`: Document summary stage data
   - `StageDataPhysician`: Physician matching stage data
   - `StageDataInstitution`: Institution matching stage data
   - `StageDataLabReport`: Lab report matching stage data

### Session Structure

The Redis session stores the processing state using the `ReportStatus` model, which includes:
- `user_id`: ID of the user who initiated the processing
- `document_summary`: Document summary stage data
- `matched_physician`: Physician matching stage data
- `matched_institution`: Institution matching stage data
- `lab_reports`: Lab report matching stage data
- `analysis`: Current processing stage and message

## API Endpoints

### OCR Processing Endpoints

1. **POST `/ocr/process`**
   - **Purpose**: Upload and process a document
   - **Parameters**:
     - `file`: The PDF file to process
     - `test_id`: Unique identifier for the processing session
     - `user_id`: ID of the user initiating the process
     - `language`: Language of the document (default: "english")
   - **Response**: Success status and test ID for tracking

2. **GET `/ocr/status/{test_id}`**
   - **Purpose**: Check the status of a processing session
   - **Parameters**:
     - `test_id`: ID of the processing session
   - **Response**: Current status of all processing stages

### Redis Management Endpoints

1. **GET `/redis/health`**
   - **Purpose**: Check Redis connection health
   - **Response**: Health status of the Redis connection

2. **POST `/redis/session/{session_id}`**
   - **Purpose**: Create a new session
   - **Parameters**:
     - `session_id`: ID for the new session
     - `session_data`: Optional initial data for the session
   - **Response**: Created session data

3. **GET `/redis/session/{session_id}`**
   - **Purpose**: Retrieve session data
   - **Parameters**:
     - `session_id`: ID of the session to retrieve
   - **Response**: Complete session data

4. **PUT `/redis/session/{session_id}/data/{key}`**
   - **Purpose**: Update a specific key in a session
   - **Parameters**:
     - `session_id`: ID of the session to update
     - `key`: Key to update
     - `data`: New value for the key
   - **Response**: Success status

### Vector Data Management Endpoints

1. **POST `/ocr/vector-data/{data_type}`**
   - **Purpose**: Update vector data for entity matching
   - **Parameters**:
     - `data_type`: Type of data to update ("doctors", "institutions", or "lab_reports")
     - `data`: JSON array of data items
   - **Response**: Success status

## Integration with External Services

### LlamaParse Integration

The application integrates with LlamaParse through the LlamaIndex API for document parsing:

1. **Document Upload**: Files are uploaded to LlamaParse using the `/upload` endpoint.
2. **Job Status Checking**: The application polls the job status until processing is complete.
3. **Result Retrieval**: Once processing is complete, the application retrieves the parsed content in a markdown format.

### LLM Integration

The application uses multiple LLM providers for different tasks:

1. **Primary Models**:
   - Claude (Anthropic): Used for document summarization and entity matching
   - GPT-4 (OpenAI): Used for complex parsing tasks
   - Llama 3 (Groq): Used as a fallback option

2. **Fallback Mechanism**: The application implements a fallback mechanism that tries alternative models if the primary model fails.

3. **Output Fixing**: If parsing fails, the application uses an OutputFixingParser to attempt to correct the output.

### Webhook Integration

The application sends webhook notifications to external systems:

1. **Stage Updates**: Notifications are sent when processing stages start or complete.
2. **Process Completion**: A notification is sent when the entire process completes.
3. **Error Notifications**: Notifications are sent when errors occur during processing.

## Error Handling and Resilience

### Error Handling Strategies

1. **LLM Fallbacks**: If the primary LLM fails, the system falls back to alternative models.
2. **Output Fixing**: If parsing fails, the system attempts to fix the output using specialized parsers.
3. **Retry Logic**: The webhook service implements retry logic with exponential backoff.
4. **Exception Handling**: Comprehensive exception handling ensures the application can recover from errors.

### Monitoring and Logging

1. **Logging**: The application uses a structured logging system to track processing steps and errors.
2. **Session Tracking**: Redis sessions provide a complete history of processing stages and status.
3. **Webhook Notifications**: External systems are notified of processing status and errors.

## Performance Considerations

### Parallel Processing

1. **Thread-based Parallelism**: Lab test parameters are processed in parallel using threads.
2. **Task Manager**: A dedicated task manager handles thread creation and management.

### Resource Management

1. **File Cleanup**: Temporary files are removed after processing.
2. **Redis Session Expiry**: Sessions can be configured to expire after a certain period.
3. **Memory Optimization**: The application uses streaming for file uploads to minimize memory usage.

## Security Considerations

1. **API Key Management**: API keys for external services are stored in environment variables.
2. **Input Validation**: All inputs are validated using Pydantic models.
3. **Session Isolation**: Each processing session is isolated by a unique ID.

## Deployment and Configuration

### Environment Variables

The application uses the following environment variables:

1. **API Keys**:
   - `LLAMA_CLOUD_API_KEY`: LlamaIndex API key
   - `OPENAI_API_KEY`: OpenAI API key
   - `CLAUDE_API_KEY`: Anthropic API key
   - `GROQ_API_KEY`: Groq API key
   - `NOMIC_API_KEY`: Nomic API key

2. **Redis Configuration**:
   - `REDIS_HOST`: Redis server hostname
   - `REDIS_PORT`: Redis server port
   - `REDIS_DB`: Redis database number

3. **Langfuse Configuration**:
   - `LANGFUSE_SECRET_KEY`: Langfuse secret key
   - `LANGFUSE_PUBLIC_KEY`: Langfuse public key
   - `LANGFUSE_HOST`: Langfuse host URL

4. **Slack Integration**:
   - `SLACK_TOKEN`: Slack API token

### Docker Configuration

The application is containerized using Docker with the following configuration:

1. **Base Image**: Python 3.11
2. **Exposed Port**: 8000
3. **Environment Variables**: Loaded from `.env` file
4. **Resource Limits**:
   - CPU: 14 cores (limit), 8 cores (reservation)
   - Memory: 32GB (limit), 24GB (reservation)

## Conclusion

The Mediboard-LLM application provides a robust solution for processing medical lab test reports, extracting structured data, and matching entities against existing databases. Its modular architecture, integration with multiple LLM providers, and comprehensive error handling make it a reliable system for healthcare document processing.
