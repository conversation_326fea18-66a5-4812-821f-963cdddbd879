[{"base_file": "base_jsons\\sample_base_json.json", "input_file": "llamaextract_results\\extraction_result_20150104_maccabi_20250602_173207.json", "comparison": {"matches": {}, "differences": {"patient_info": {"input": {"first_name": "מיתר", "last_name": "שמואל"}, "base": "KEY_NOT_FOUND"}, "physician_info": {"input": {"first_name": "בו<PERSON><PERSON>", "last_name": "גבע"}, "base": "KEY_NOT_FOUND"}, "medical_facility": {"input": {"facility_name": "<PERSON><PERSON><PERSON><PERSON>", "location": null}, "base": "KEY_NOT_FOUND"}, "is_lab_report": {"input": true, "base": "KEY_NOT_FOUND"}, "test_date": {"input": "04/01/2012", "base": "KEY_NOT_FOUND"}, "lab_reports": {"input": [{"name": "glucose (b)", "result": "103", "range": "70-100", "units": "mg/dl", "test_type": "כימיה בדם", "comment": null, "comment_english": null, "index": 0, "result_value_type": "numeric_value"}, {"name": "urea (b)", "result": "30", "range": "17-49", "units": "mg/dl", "test_type": "כימיה בדם", "comment": null, "comment_english": null, "index": 1, "result_value_type": "numeric_value"}, {"name": "k+ potassium (b)", "result": "4.2", "range": "3.5-5.1", "units": "mmol/l", "test_type": "כימיה בדם", "comment": null, "comment_english": null, "index": 2, "result_value_type": "numeric_value"}, {"name": "na- sodium (b)", "result": "137", "range": "136-146", "units": "mmol/l", "test_type": "כימיה בדם", "comment": null, "comment_english": null, "index": 3, "result_value_type": "numeric_value"}, {"name": "protein total (b)", "result": "6.9", "range": "6.6-8.3", "units": "g/dl", "test_type": "כימיה בדם", "comment": null, "comment_english": null, "index": 4, "result_value_type": "numeric_value"}, {"name": "albumin (b)", "result": "4.3", "range": "3.5-5.2", "units": "g/dl", "test_type": "כימיה בדם", "comment": null, "comment_english": null, "index": 5, "result_value_type": "numeric_value"}, {"name": "alt (gpt)", "result": "39.4", "range": "0-37", "units": "u/l", "test_type": "כימיה בדם", "comment": null, "comment_english": null, "index": 6, "result_value_type": "numeric_value"}, {"name": "ast (got)", "result": "25", "range": "10-37", "units": "u/l", "test_type": "כימיה בדם", "comment": null, "comment_english": null, "index": 7, "result_value_type": "numeric_value"}, {"name": "ck", "result": "70", "range": "0-170", "units": "u/l", "test_type": "כימיה בדם", "comment": null, "comment_english": null, "index": 8, "result_value_type": "numeric_value"}, {"name": "esr 1 hour", "result": "20", "range": "0-20", "units": "mm/hr", "test_type": "המטולוגיה-כללי", "comment": null, "comment_english": null, "index": 9, "result_value_type": "numeric_value"}, {"name": "wbc-leucocytes", "result": "10.1", "range": "4.5-11", "units": "10*3/micl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 10, "result_value_type": "numeric_value"}, {"name": "rbc-red blood cells", "result": "4.69", "range": "4.5-5.5", "units": "10*6/micl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 11, "result_value_type": "numeric_value"}, {"name": "hemoglobin", "result": "14.2", "range": "13.5-17.5", "units": "g/dl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 12, "result_value_type": "numeric_value"}, {"name": "hematocrit", "result": "41.7", "range": "41-53", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 13, "result_value_type": "numeric_value"}, {"name": "mcv-mean cell volume", "result": "88.9", "range": "79-97", "units": "fl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 14, "result_value_type": "numeric_value"}, {"name": "mch-mean cell hemoglobin", "result": "30.3", "range": "27-34", "units": "pg/cell", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 15, "result_value_type": "numeric_value"}, {"name": "mchc-m.cell hb cont.", "result": "34.1", "range": "32-36", "units": "g/dl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 16, "result_value_type": "numeric_value"}, {"name": "rdw-red cell distri.width", "result": "13", "range": "11.6-15", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 17, "result_value_type": "numeric_value"}, {"name": "platelets", "result": "185", "range": "150-450", "units": "10*3/micl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 18, "result_value_type": "numeric_value"}, {"name": "mpv-mean platelet volume", "result": "11.3", "range": "8.5-12.9", "units": "fl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 19, "result_value_type": "numeric_value"}, {"name": "neutrophils %", "result": "61.9", "range": "40-75", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 20, "result_value_type": "numeric_value"}, {"name": "lymphocytes %", "result": "25", "range": "22-44", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 21, "result_value_type": "numeric_value"}, {"name": "monocytes %", "result": "12.1", "range": "3-13", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 22, "result_value_type": "numeric_value"}, {"name": "eosinophils %", "result": "0.7", "range": "0-6", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 23, "result_value_type": "numeric_value"}, {"name": "basophils %", "result": "0.3", "range": "0-2", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 24, "result_value_type": "numeric_value"}, {"name": "neutrophils #", "result": "6.23", "range": "1.8-7.7", "units": "#", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 25, "result_value_type": "numeric_value"}, {"name": "lymphocytes #", "result": "2.52", "range": "1-4.8", "units": "#", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 26, "result_value_type": "numeric_value"}, {"name": "monocytes #", "result": "1.22", "range": "0-1.1", "units": "#", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 27, "result_value_type": "numeric_value"}, {"name": "eosinophils #", "result": "0.07", "range": "0-0.6", "units": "#", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 28, "result_value_type": "numeric_value"}, {"name": "basophils #", "result": "0.03", "range": "0-0.2", "units": "#", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 29, "result_value_type": "numeric_value"}], "base": {"progress": 100, "status": "complete", "data": [{"match_data": {"id": 128, "test_type": "complete blood count (cbc)", "parameter": "neutrophils % (neut%)", "sample_type": "blood"}, "match_parameter_info": {"match_score": "similar: typo", "reason": "the parameter 'neutrophils %' matches conceptually with the parameter 'neutrophils % (neut%)' with minor differences in formatting."}, "match_test_type_info": {"match_score": "unknown", "reason": "the reported test type 'המטולוגיה- ספירת דם - מכ<PERSON><PERSON><PERSON>' does not match any known test types in the index."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 61.9, "unit": "%"}, "conversion_details": null, "comment": "the result unit '%' is the same as the reference unit. no conversion required."}, "test_params": {"name": "neutrophils %", "result": 61.9, "range": "40-75", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 20, "result_value_type": "numeric_value"}}, {"match_data": {"id": 397, "test_type": "metabolic panel", "parameter": "glucose (glu) - blood", "sample_type": "blood"}, "match_parameter_info": {"match_score": "similar: typo", "reason": "the lab report parameter 'glucose (b)' matches conceptually with 'glucose (glu) - blood' but has a slight difference in spelling (b vs glu)."}, "match_test_type_info": {"match_score": "unknown", "reason": "the test type in the lab report 'כימיה בדם' has no equivalent match in the index."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 103, "unit": "mg/dl"}, "conversion_details": null, "comment": "no conversion needed as the result is already in the reference unit (mg/dl)."}, "test_params": {"name": "glucose (b)", "result": 103, "range": "70-100", "units": "mg/dl", "test_type": "כימיה בדם", "comment": null, "comment_english": null, "index": 0, "result_value_type": "numeric_value"}}, {"match_data": {"id": 463, "test_type": "metabolic panel", "parameter": "urea nitrogen (bun) - blood", "sample_type": "blood"}, "match_parameter_info": {"match_score": "similar: typo", "reason": "the parameter 'urea (b)' in the lab report matches closely with 'urea nitrogen (bun) - blood', considering minor formatting differences."}, "match_test_type_info": {"match_score": "similar: typo", "reason": "the test type 'כימיה בדם' translates to 'metabolic panel', which is the matched test type in the context."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 30, "unit": "mg/dl"}, "conversion_details": {"factor": null, "calculation": null}, "comment": "the result is in the same unit as the reference unit, so no conversion was necessary."}, "test_params": {"name": "urea (b)", "result": 30, "range": "17-49", "units": "mg/dl", "test_type": "כימיה בדם", "comment": null, "comment_english": null, "index": 1, "result_value_type": "numeric_value"}}, {"match_data": {"id": 337, "test_type": "metabolic panel", "parameter": "alanine amino transferase, alt (gpt) - blood", "sample_type": "blood"}, "match_parameter_info": {"match_score": "similar: typo", "reason": "parameter name matches closely despite minor formatting differences."}, "match_test_type_info": {"match_score": "similar: typo", "reason": "test type matches closely even with different case formatting."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 39.4, "unit": "u/l"}, "conversion_details": {"factor": null, "calculation": null}, "comment": "the result is already in the reference unit."}, "test_params": {"name": "alt (gpt)", "result": 39.4, "range": "0-37", "units": "u/l", "test_type": "כימיה בדם", "comment": null, "comment_english": null, "index": 6, "result_value_type": "numeric_value"}}, {"match_data": {"id": 113, "test_type": "complete blood count (cbc)", "parameter": "basophils % (baso%)", "sample_type": "blood"}, "match_parameter_info": {"match_score": "similar: typo", "reason": "the parameter name 'basophils %' matches with 'basophils % (baso%)' in the database with slight difference in wording for test type."}, "match_test_type_info": {"match_score": "alternative", "reason": "the test type as 'hematology - complete blood count - device' is conceptually similar to 'complete blood count (cbc)' but uses different phrasing."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 0.3, "unit": "%"}, "conversion_details": null, "comment": "no conversion needed as the unit is the same as the reference unit."}, "test_params": {"name": "basophils %", "result": 0.3, "range": "0-2", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 24, "result_value_type": "numeric_value"}}, {"match_data": {"id": 119, "test_type": "complete blood count (cbc)", "parameter": "lymphocytes % (lym%)", "sample_type": "blood"}, "match_parameter_info": {"match_score": "similar: typo", "reason": "the parameter name matches exactly in the database, with only minor formatting differences."}, "match_test_type_info": {"match_score": "similar: typo", "reason": "the test type in the lab report closely resembles the test type in the database with minor variations."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 25, "unit": "%"}, "conversion_details": null, "comment": "the result is already in the reference unit."}, "test_params": {"name": "lymphocytes %", "result": 25, "range": "22-44", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 21, "result_value_type": "numeric_value"}}, {"match_data": {"id": 352, "test_type": "metabolic panel", "parameter": "aspratate aminotransferase, ast (got) - blood", "sample_type": "blood"}, "match_parameter_info": {"match_score": "similar: typo", "reason": "the parameter name 'ast (got)' is conceptually the same as 'aspratate aminotransferase, ast (got) - blood' with differences in formatting."}, "match_test_type_info": {"match_score": "similar: typo", "reason": "the test type 'כימיה בדם' translates to 'metabolic panel', which is similar with slight language differences."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 25, "unit": "u/l"}, "conversion_details": {"factor": null, "calculation": null}, "comment": "the result is already in the reference unit."}, "test_params": {"name": "ast (got)", "result": 25, "range": "10-37", "units": "u/l", "test_type": "כימיה בדם", "comment": null, "comment_english": null, "index": 7, "result_value_type": "numeric_value"}}, {"match_data": {"id": 115, "test_type": "complete blood count (cbc)", "parameter": "eosinophils % (eos%)", "sample_type": "blood"}, "match_parameter_info": {"match_score": "similar: typo", "reason": "the parameter name 'eosinophils %' from the lab report closely matches 'eosinophils % (eos%)' in the document, with minor differences in formatting."}, "match_test_type_info": {"match_score": "similar: typo", "reason": "the test type 'המטולוגיה- ספירת דם - מכש<PERSON><PERSON>' translates to 'hematology - complete blood count - device', which is equivalent to 'complete blood count (cbc)' in the document."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 0.7, "unit": "%"}, "conversion_details": null, "comment": "the result is in the same unit as the reference."}, "test_params": {"name": "eosinophils %", "result": 0.7, "range": "0-6", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 23, "result_value_type": "numeric_value"}}, {"match_data": {"id": 692, "test_type": "metabolic panel", "parameter": "esr", "sample_type": "blood"}, "match_parameter_info": {"match_score": "similar: typo", "reason": "the parameter name 'esr' matches exactly, minor formatting with unit differences."}, "match_test_type_info": {"match_score": "unknown", "reason": "the test type 'hematology-general' does not match the document index."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 20, "unit": "mm/hr"}, "conversion_details": null, "comment": "no conversion needed as the units are equivalent."}, "test_params": {"name": "esr 1 hour", "result": 20, "range": "0-20", "units": "mm/hr", "test_type": "המטולוגיה-כללי", "comment": null, "comment_english": null, "index": 9, "result_value_type": "numeric_value"}}, {"match_data": {"id": 121, "test_type": "complete blood count (cbc)", "parameter": "mch", "sample_type": "blood"}, "match_parameter_info": {"match_score": "similar: typo", "reason": "parameter 'mch-mean cell hemoglobin' matches 'mch' with slight format differences."}, "match_test_type_info": {"match_score": "alternative", "reason": "test type 'המטולוגיה- ספירת דם - מכש<PERSON><PERSON>' is an alternative name for 'complete blood count (cbc)'."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 30.3, "unit": "pg"}, "conversion_details": null, "comment": "no conversion needed, provided unit is compatible with the reference unit."}, "test_params": {"name": "mch-mean cell hemoglobin", "result": 30.3, "range": "27-34", "units": "pg/cell", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 15, "result_value_type": "numeric_value"}}, {"match_data": {"id": 129, "test_type": "complete blood count (cbc)", "parameter": "neutrophils no. (neut#)", "sample_type": "blood"}, "match_parameter_info": {"match_score": "similar: typo", "reason": "the parameter 'neutrophils #' matches 'neutrophils no. (neut#)' with a minor formatting difference in units."}, "match_test_type_info": {"match_score": "similar: typo", "reason": "the test type 'hematology- blood count' is conceptually similar to 'complete blood count (cbc)'."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 6.23, "unit": "10e3/microl"}, "conversion_details": {"factor": 1, "calculation": "result remains the same as it was already in the conversion unit."}, "comment": "the result was in units '#' which is interpreted as equivalent to '10e3/microl'."}, "test_params": {"name": "neutrophils #", "result": 6.23, "range": "1.8-7.7", "units": "#", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 25, "result_value_type": "numeric_value"}}, {"match_data": {"id": 126, "test_type": "complete blood count (cbc)", "parameter": "monocytes no. (mono#)", "sample_type": "blood"}, "match_parameter_info": {"match_score": "similar: typo", "reason": "parameter names are conceptually similar, with a minor discrepancy in nomenclature."}, "match_test_type_info": {"match_score": "unknown", "reason": "test types do not match as they use different terminology."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 1.22, "unit": "10e3/microl"}, "conversion_details": {"factor": 1, "calculation": "1.22 cells * (1/1000) * (1000) = 1.22"}, "comment": "conversion was successful from # to 10e3/microl."}, "test_params": {"name": "monocytes #", "result": 1.22, "range": "0-1.1", "units": "#", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 27, "result_value_type": "numeric_value"}}, {"match_data": {"id": 122, "test_type": "complete blood count (cbc)", "parameter": "mchc", "sample_type": "blood"}, "match_parameter_info": {"match_score": "similar: typo", "reason": "the parameter 'mchc-m.cell hb cont.' aligns with 'mchc' from the context data, allowing for variations like case differences."}, "match_test_type_info": {"match_score": "similar: typo", "reason": "the test type in the report matches closely with 'complete blood count (cbc)' from the context data, accounting for a minor typographical variation."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 34.1, "unit": "g/dl"}, "conversion_details": null, "comment": "the result is already in the reference unit."}, "test_params": {"name": "mchc-m.cell hb cont.", "result": 34.1, "range": "32-36", "units": "g/dl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 16, "result_value_type": "numeric_value"}}, {"match_data": {"id": 124, "test_type": "complete blood count (cbc)", "parameter": "mean platelet volume (mpv)", "sample_type": "blood"}, "match_parameter_info": {"match_score": "similar: typo", "reason": "the lab report parameter 'mpv-mean platelet volume' matches the existing parameter 'mean platelet volume (mpv)' with minor discrepancies in spacing and wording."}, "match_test_type_info": {"match_score": "similar: typo", "reason": "the test type 'hematology-complete blood count' is a similar expression of 'complete blood count (cbc)' with slight variations."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 11.3, "unit": "fl"}, "conversion_details": null, "comment": "no conversion needed as the unit matches the reference unit."}, "test_params": {"name": "mpv-mean platelet volume", "result": 11.3, "range": "8.5-12.9", "units": "fl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": "החל מ-4.11.08 יש לב לשיפור ערכי הייחוס.", "comment_english": "as of 4.11.08, attention to improvement in reference values.", "index": 19, "result_value_type": "numeric_value"}}, {"match_data": {"id": 116, "test_type": "complete blood count (cbc)", "parameter": "eosinophils no. (eos#)", "sample_type": "blood"}, "match_parameter_info": {"match_score": "alternative", "reason": "parameter name matches conceptually with alternative medical terminology."}, "match_test_type_info": {"match_score": "similar: typo", "reason": "test type is very similar under the same context (cbc)."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 0.07, "unit": "10e3/microl"}, "conversion_details": {"factor": null, "calculation": null}, "comment": "converted result to standardized unit of 10e3/microl."}, "test_params": {"name": "eosinophils #", "result": 0.07, "range": "0-0.6", "units": "#", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 28, "result_value_type": "numeric_value"}}, {"match_data": {"id": 114, "test_type": "complete blood count (cbc)", "parameter": "basophils no. (baso#)", "sample_type": "blood"}, "match_parameter_info": {"match_score": "similar: typo", "reason": "the parameter name matches conceptually with minor formatting differences."}, "match_test_type_info": {"match_score": "similar: typo", "reason": "the test type matches conceptually with minor differences in capitalization and format."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 30, "unit": "10e3/microl"}, "conversion_details": {"factor": 1000, "calculation": "0.03 # * 1000 = 30 10e3/microl"}, "comment": "successfully converted from # to 10e3/microl."}, "test_params": {"name": "basophils #", "result": 0.03, "range": "0-0.2", "units": "#", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 29, "result_value_type": "numeric_value"}}, {"match_data": {"id": 338, "test_type": "metabolic panel", "parameter": "albumin (alb) - blood", "sample_type": "blood"}, "match_parameter_info": {"match_score": "similar: typo", "reason": "the parameter name 'albumin (b)' is conceptually similar to 'albumin (alb)' in document 1, but naming varies slightly."}, "match_test_type_info": {"match_score": "similar: typo", "reason": "the test type 'chemistry' is conceptually similar to 'metabolic panel'."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 43, "unit": "gr/l"}, "conversion_details": {"factor": 10, "calculation": "4.3 g/dl * 10 = 43 gr/l"}, "comment": "converted from g/dl to gr/l."}, "test_params": {"name": "albumin (b)", "result": 4.3, "range": "3.5-5.2", "units": "g/dl", "test_type": "כימיה בדם", "comment": null, "comment_english": null, "index": 5, "result_value_type": "numeric_value"}}, {"match_data": {"id": 117, "test_type": "complete blood count (cbc)", "parameter": "hematocrit (hct)", "sample_type": "blood"}, "match_parameter_info": {"match_score": "similar: typo", "reason": "the parameter 'hematocrit' matches perfectly with 'hematocrit (hct)' in the database with minor variations in labeling."}, "match_test_type_info": {"match_score": "similar: typo", "reason": "the test type 'hematology - blood count - machine' is a broader classification of 'complete blood count (cbc)', showing a similar category."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 41.7, "unit": "%"}, "conversion_details": null, "comment": "no conversion needed as the unit is already in the reference unit."}, "test_params": {"name": "hematocrit", "result": 41.7, "range": "41-53", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 13, "result_value_type": "numeric_value"}}, {"match_data": {"id": 455, "test_type": "metabolic panel", "parameter": "sodium (na) - blood", "sample_type": "blood"}, "match_parameter_info": {"match_score": "similar: typo", "reason": "the parameter 'sodium (na)' is a direct match to 'na- sodium (b)', with minor differences in naming."}, "match_test_type_info": {"match_score": "similar: typo", "reason": "the test type 'metabolic panel' closely relates to 'כימיה בדם' (blood chemistry), showing a conceptually similar relation."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 137, "unit": "mmol/l"}, "conversion_details": {"factor": null, "calculation": null}, "comment": "no conversion needed, result is already in the reference unit."}, "test_params": {"name": "na- sodium (b)", "result": 137, "range": "136-146", "units": "mmol/l", "test_type": "כימיה בדם", "comment": null, "comment_english": null, "index": 3, "result_value_type": "numeric_value"}}, {"match_data": {"id": 374, "test_type": "metabolic panel", "parameter": "creatine kinase (ck, cpk) - blood", "sample_type": "blood"}, "match_parameter_info": {"match_score": "similar: typo", "reason": "the parameter name matches conceptually but has formatting differences in case."}, "match_test_type_info": {"match_score": "similar: typo", "reason": "the test type matches exactly with the documented index."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 70, "unit": "u/l"}, "conversion_details": {"factor": null, "calculation": null}, "comment": "no conversion needed as the units match."}, "test_params": {"name": "ck", "result": 70, "range": "0-170", "units": "u/l", "test_type": "כימיה בדם", "comment": null, "comment_english": null, "index": 8, "result_value_type": "numeric_value"}}, {"match_data": {"id": 118, "test_type": "complete blood count (cbc)", "parameter": "hemoglobin (hb)", "sample_type": "blood"}, "match_parameter_info": {"match_score": "similar: typo", "reason": "exact match by parameter name and corresponding unit."}, "match_test_type_info": {"match_score": "similar: typo", "reason": "test type is similar with capitalization difference; it's an exact content match."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 14.2, "unit": "g/dl"}, "conversion_details": null, "comment": "the unit is already in the reference unit; no conversion needed."}, "test_params": {"name": "hemoglobin", "result": 14.2, "range": "13.5-17.5", "units": "g/dl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 12, "result_value_type": "numeric_value"}}, {"match_data": {"id": 136, "test_type": "complete blood count (cbc)", "parameter": "wbc", "sample_type": "blood"}, "match_parameter_info": {"match_score": "similar: typo", "reason": "the parameter wbc-leucocytes closely matches wbc, with minor differences in terminology."}, "match_test_type_info": {"match_score": "similar: typo", "reason": "test type name similarity with minor variations."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 10.1, "unit": "10e3/microl"}, "conversion_details": null, "comment": "the unit is already in the reference unit."}, "test_params": {"name": "wbc-leucocytes", "result": 10.1, "range": "4.5-11", "units": "10^3/micl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 10, "result_value_type": "numeric_value"}}, {"match_data": {"id": 443, "test_type": "metabolic panel", "parameter": "potassium (k) - blood", "sample_type": "blood"}, "match_parameter_info": {"match_score": "similar: typo", "reason": "the parameter names match with minor formatting differences."}, "match_test_type_info": {"match_score": "alternative", "reason": "the test types are conceptually similar."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 4.2, "unit": "mmol/l"}, "conversion_details": null, "comment": "the result unit matches the reference unit, no conversion needed."}, "test_params": {"name": "k+ potassium (b)", "result": 4.2, "range": "3.5-5.1", "units": "mmol/l", "test_type": "כימיה בדם", "comment": null, "comment_english": null, "index": 2, "result_value_type": "numeric_value"}}, {"match_data": {"id": 131, "test_type": "complete blood count (cbc)", "parameter": "platelet count (plt)", "sample_type": "blood"}, "match_parameter_info": {"match_score": "similar: typo", "reason": "the parameter name 'platelet count (plt)' matches conceptually with 'platelets' despite some formatting differences."}, "match_test_type_info": {"match_score": "alternative", "reason": "the test type 'complete blood count (cbc)' is conceptually related to 'hematology - blood count - device'."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 185, "unit": "10e3/microl"}, "conversion_details": {"factor": null, "calculation": null}, "comment": "the original units matched the reference unit, hence no conversion was necessary."}, "test_params": {"name": "platelets", "result": 185, "range": "150-450", "units": "10^3/micl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 18, "result_value_type": "numeric_value"}}, {"match_data": {"id": 125, "test_type": "complete blood count (cbc)", "parameter": "monocytes % (mono%)", "sample_type": "blood"}, "match_parameter_info": {"match_score": "similar: typo", "reason": "the parameter matches exactly in name and unit with possible minor differences in formatting (e.g., case sensitivity)."}, "match_test_type_info": {"match_score": "similar: typo", "reason": "test type from lab report can be interpreted as related to complete blood count testing upon translation."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 12.1, "unit": "%"}, "conversion_details": null, "comment": "no conversion was needed as the unit of measurement is the same as the reference."}, "test_params": {"name": "monocytes %", "result": 12.1, "range": "3-13", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 22, "result_value_type": "numeric_value"}}, {"match_data": {"id": 133, "test_type": "complete blood count (cbc)", "parameter": "rdw", "sample_type": "blood"}, "match_parameter_info": {"match_score": "similar: typo", "reason": "exact match in the parameter name with minor differences in case."}, "match_test_type_info": {"match_score": "similar: typo", "reason": "exact match for the test type with case differences only."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 13, "unit": "%"}, "conversion_details": null, "comment": "no conversion needed; the units are equivalent."}, "test_params": {"name": "rdw-red cell distri.width", "result": 13, "range": "11.6-15", "units": "%", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 17, "result_value_type": "numeric_value"}}, {"match_data": {"id": 132, "test_type": "complete blood count (cbc)", "parameter": "rbc", "sample_type": "blood"}, "match_parameter_info": {"match_score": "similar: typo", "reason": "exact match for the parameter name 'rbc'."}, "match_test_type_info": {"match_score": "similar: typo", "reason": "test type 'complete blood count (cbc)' aligns with report's test type."}, "unit_conversion": {"status": "no_conversion", "standardized": {"result": 4.69, "unit": "10e6/microl"}, "conversion_details": null, "comment": "units are equivalent, no conversion required."}, "test_params": {"name": "rbc-red blood cells", "result": 4.69, "range": "4.5-5.5", "units": "10^6/micl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 11, "result_value_type": "numeric_value"}}, {"match_data": {"id": 120, "test_type": "complete blood count (cbc)", "parameter": "lymphocytes no. (lym#)", "sample_type": "blood"}, "match_parameter_info": {"match_score": "similar: typo", "reason": "the parameter 'lymphocytes #' is similar to 'lymphocytes no. (lym#)' with a minor difference in naming format."}, "match_test_type_info": {"match_score": "alternative", "reason": "the test type 'hematology - blood count - device' is considered an alternative name for 'complete blood count (cbc)'."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 2.52, "unit": "10e3/microl"}, "conversion_details": {"factor": 1, "calculation": "conversion from cells/ul to cells/ul is a direct match."}, "comment": "the report parameter 'lymphocytes #' can be expressed in the standardized unit as cells per microliter."}, "test_params": {"name": "lymphocytes #", "result": 2.52, "range": "1-4.8", "units": "#", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 26, "result_value_type": "numeric_value"}}, {"match_data": {"id": 448, "test_type": "metabolic panel", "parameter": "protein total (tp) - blood", "sample_type": "blood"}, "match_parameter_info": {"match_score": "similar: typo", "reason": "parameter name matches conceptually with minor naming differences."}, "match_test_type_info": {"match_score": "similar: typo", "reason": "test type is conceptually similar to the provided test type in context."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 69, "unit": "gr/l"}, "conversion_details": {"factor": 10, "calculation": "6.9 g/dl × 10 = 69 gr/l"}, "comment": "conversion performed from g/dl to gr/l."}, "test_params": {"name": "protein total (b)", "result": 6.9, "range": "6.6-8.3", "units": "g/dl", "test_type": "כימיה בדם", "comment": null, "comment_english": null, "index": 4, "result_value_type": "numeric_value"}}, {"match_data": {"id": 536, "test_type": "complete blood count (cbc)", "parameter": "mcvr", "sample_type": "blood"}, "match_parameter_info": {"match_score": "similar: typo", "reason": "the parameter name mcv closely resembles the stored parameter name mcvr with minor formatting differences."}, "match_test_type_info": {"match_score": "similar: typo", "reason": "the test type in the report related closely to the complete blood count, matching the analysis."}, "unit_conversion": {"status": "conversion_pass", "standardized": {"result": 88.9, "unit": "fl"}, "conversion_details": {"factor": null, "calculation": null}, "comment": "no conversion needed as the unit matches the reference unit."}, "test_params": {"name": "mcv-mean cell volume", "result": 88.9, "range": "79-97", "units": "fl", "test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "comment": null, "comment_english": null, "index": 14, "result_value_type": "numeric_value"}}]}}}, "total_fields": 6, "match_percentage": 0.0}}]