[{"base_file": "base_jsons\\second_base_json.json", "input_file": "llamaextract_results\\llamaextract_code_maccabi.json", "comparison": {"matches": {"patient_info.first_name": "אילנה", "physician_info.first_name": "אירנה", "physician_info.last_name": "אלון", "is_lab_report": true, "lab_reports[0].name": "glucose (b)", "lab_reports[0].units": "mg/dl", "lab_reports[0].test_type": "כימיה בדם", "lab_reports[0].index": 0, "lab_reports[0].result_value_type": "numeric_value", "lab_reports[1].name": "urea (b)", "lab_reports[1].units": "mg/dl", "lab_reports[1].test_type": "כימיה בדם", "lab_reports[1].index": 1, "lab_reports[1].result_value_type": "numeric_value", "lab_reports[2].name": "creatinine (b)", "lab_reports[2].units": "mg/dl", "lab_reports[2].test_type": "כימיה בדם", "lab_reports[2].index": 2, "lab_reports[2].result_value_type": "numeric_value", "lab_reports[3].name": "egfr", "lab_reports[3].units": "ml/min/1.73m²", "lab_reports[3].test_type": "כימיה בדם", "lab_reports[3].index": 3, "lab_reports[3].result_value_type": "numeric_value", "lab_reports[4].name": "k+ potassium (b)", "lab_reports[4].units": "mmol/l", "lab_reports[4].test_type": "כימיה בדם", "lab_reports[4].index": 4, "lab_reports[4].result_value_type": "numeric_value", "lab_reports[5].name": "na- sodium (b)", "lab_reports[5].units": "mmol/l", "lab_reports[5].test_type": "כימיה בדם", "lab_reports[5].index": 5, "lab_reports[5].result_value_type": "numeric_value", "lab_reports[6].name": "alkp-alkaline phosphatase", "lab_reports[6].units": "u/l", "lab_reports[6].test_type": "כימיה בדם", "lab_reports[6].index": 6, "lab_reports[6].result_value_type": "numeric_value", "lab_reports[7].name": "alt (gpt)", "lab_reports[7].units": "u/l", "lab_reports[7].test_type": "כימיה בדם", "lab_reports[7].index": 7, "lab_reports[7].result_value_type": "numeric_value", "lab_reports[8].name": "ast (got)", "lab_reports[8].units": "u/l", "lab_reports[8].test_type": "כימיה בדם", "lab_reports[8].index": 8, "lab_reports[8].result_value_type": "numeric_value", "lab_reports[9].name": "fe - iron", "lab_reports[9].units": "micg/dl", "lab_reports[9].test_type": "כימיה בדם", "lab_reports[9].index": 9, "lab_reports[9].result_value_type": "numeric_value", "lab_reports[10].name": "ferritin", "lab_reports[10].units": "ng/ml", "lab_reports[10].test_type": "כימיה בדם", "lab_reports[10].index": 10, "lab_reports[10].result_value_type": "numeric_value", "lab_reports[11].name": "folic acid", "lab_reports[11].range": ">5.4", "lab_reports[11].units": "ng/ml", "lab_reports[11].test_type": "כימיה בדם", "lab_reports[11].index": 11, "lab_reports[11].result_value_type": "numeric_value", "lab_reports[12].name": "wbc-leucocytes", "lab_reports[12].test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "lab_reports[12].comment": null, "lab_reports[12].comment_english": null, "lab_reports[12].index": 12, "lab_reports[12].result_value_type": "numeric_value", "lab_reports[13].name": "rbc-red blood cells", "lab_reports[13].test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "lab_reports[13].comment": null, "lab_reports[13].comment_english": null, "lab_reports[13].index": 13, "lab_reports[13].result_value_type": "numeric_value", "lab_reports[14].name": "hemoglobin", "lab_reports[14].units": "g/dl", "lab_reports[14].test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "lab_reports[14].comment": null, "lab_reports[14].comment_english": null, "lab_reports[14].index": 14, "lab_reports[14].result_value_type": "numeric_value", "lab_reports[15].name": "hematocrit", "lab_reports[15].units": "%", "lab_reports[15].test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "lab_reports[15].comment": null, "lab_reports[15].comment_english": null, "lab_reports[15].index": 15, "lab_reports[15].result_value_type": "numeric_value", "lab_reports[16].name": "mcv-mean cell volume", "lab_reports[16].units": "fl", "lab_reports[16].test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "lab_reports[16].comment": null, "lab_reports[16].comment_english": null, "lab_reports[16].index": 16, "lab_reports[16].result_value_type": "numeric_value", "lab_reports[17].name": "mch-mean cell hemoglobin", "lab_reports[17].units": "pg/cell", "lab_reports[17].test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "lab_reports[17].comment": null, "lab_reports[17].comment_english": null, "lab_reports[17].index": 17, "lab_reports[17].result_value_type": "numeric_value", "lab_reports[18].name": "mchc-m.cell hb cont.", "lab_reports[18].units": "g/dl", "lab_reports[18].test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "lab_reports[18].comment": null, "lab_reports[18].comment_english": null, "lab_reports[18].index": 18, "lab_reports[18].result_value_type": "numeric_value", "lab_reports[19].name": "rdw-red cell distri.width", "lab_reports[19].units": "%", "lab_reports[19].test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "lab_reports[19].comment": null, "lab_reports[19].comment_english": null, "lab_reports[19].index": 19, "lab_reports[19].result_value_type": "numeric_value", "lab_reports[20].name": "platelets", "lab_reports[20].units": "10^3/micl", "lab_reports[20].test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "lab_reports[20].comment": null, "lab_reports[20].comment_english": null, "lab_reports[20].index": 20, "lab_reports[20].result_value_type": "numeric_value", "lab_reports[21].name": "mpv-mean platelet volume", "lab_reports[21].units": "fl", "lab_reports[21].test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "lab_reports[21].comment": null, "lab_reports[21].comment_english": null, "lab_reports[21].index": 21, "lab_reports[21].result_value_type": "numeric_value", "lab_reports[22].name": "neutrophils %", "lab_reports[22].units": "%", "lab_reports[22].test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "lab_reports[22].comment": null, "lab_reports[22].comment_english": null, "lab_reports[22].index": 22, "lab_reports[22].result_value_type": "numeric_value", "lab_reports[23].name": "lymphocytes %", "lab_reports[23].units": "%", "lab_reports[23].test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "lab_reports[23].comment": null, "lab_reports[23].comment_english": null, "lab_reports[23].index": 23, "lab_reports[23].result_value_type": "numeric_value", "lab_reports[24].name": "monocytes %", "lab_reports[24].units": "%", "lab_reports[24].test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "lab_reports[24].comment": null, "lab_reports[24].comment_english": null, "lab_reports[24].index": 24, "lab_reports[24].result_value_type": "numeric_value", "lab_reports[25].name": "eosinophils %", "lab_reports[25].units": "%", "lab_reports[25].test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "lab_reports[25].comment": null, "lab_reports[25].comment_english": null, "lab_reports[25].index": 25, "lab_reports[25].result_value_type": "numeric_value", "lab_reports[26].name": "basophils %", "lab_reports[26].units": "%", "lab_reports[26].test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "lab_reports[26].comment": null, "lab_reports[26].comment_english": null, "lab_reports[26].index": 26, "lab_reports[26].result_value_type": "numeric_value", "lab_reports[27].name": "neutrophils #", "lab_reports[27].units": "10^3/micl", "lab_reports[27].test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "lab_reports[27].comment": null, "lab_reports[27].comment_english": null, "lab_reports[27].index": 27, "lab_reports[27].result_value_type": "numeric_value", "lab_reports[28].name": "lymphocytes #", "lab_reports[28].units": "10^3/micl", "lab_reports[28].test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "lab_reports[28].comment": null, "lab_reports[28].comment_english": null, "lab_reports[28].index": 28, "lab_reports[28].result_value_type": "numeric_value", "lab_reports[29].name": "monocytes #", "lab_reports[29].units": "10^3/micl", "lab_reports[29].test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "lab_reports[29].comment": null, "lab_reports[29].comment_english": null, "lab_reports[29].index": 29, "lab_reports[29].result_value_type": "numeric_value", "lab_reports[30].name": "eosinophils #", "lab_reports[30].units": "10^3/micl", "lab_reports[30].test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "lab_reports[30].comment": null, "lab_reports[30].comment_english": null, "lab_reports[30].index": 30, "lab_reports[30].result_value_type": "numeric_value", "lab_reports[31].name": "basophils #", "lab_reports[31].units": "10^3/micl", "lab_reports[31].test_type": "המטולוגיה- ס<PERSON><PERSON>ר<PERSON> דם - מכ<PERSON><PERSON>ר", "lab_reports[31].comment": null, "lab_reports[31].comment_english": null, "lab_reports[31].index": 31, "lab_reports[31].result_value_type": "numeric_value", "lab_reports[32].name": "pt (seconds)", "lab_reports[32].units": "sec.", "lab_reports[32].test_type": "קרישה", "lab_reports[32].index": 32, "lab_reports[32].result_value_type": "numeric_value", "lab_reports[33].name": "i.n.r", "lab_reports[33].range": null, "lab_reports[33].units": null, "lab_reports[33].test_type": "קרישה", "lab_reports[33].index": 33, "lab_reports[33].result_value_type": "numeric_value", "lab_reports[34].name": "ptt", "lab_reports[34].units": "sec.", "lab_reports[34].test_type": "קרישה", "lab_reports[34].comment": null, "lab_reports[34].comment_english": null, "lab_reports[34].index": 34, "lab_reports[34].result_value_type": "numeric_value", "lab_reports[35].name": "tsh", "lab_reports[35].units": "miu/l", "lab_reports[35].test_type": "אנדוקרינולוגיה", "lab_reports[35].index": 35, "lab_reports[35].result_value_type": "numeric_value"}, "differences": {"patient_info.last_name": {"input": "ל<PERSON><PERSON><PERSON>", "base": "אלגרבלי"}, "medical_facility": {"input": null, "base": {"facility_name": null, "location": null}}, "test_date": {"input": "08/06/23", "base": "2023-06-08t00:00:00"}, "lab_reports[0].result": {"input": "81", "base": 81}, "lab_reports[0].range": {"input": "70 - 100", "base": "70-100"}, "lab_reports[0].comment": {"input": "הערות לבדיקה: מיום 20.1.23 חל שינוי בערכת הבדיקה", "base": "מיום 20.1.23 חל שינוי בערכת הבדיקה"}, "lab_reports[0].comment_english": {"input": "test note: as of 20.1.23, there has been a change in the test kit.", "base": "from 20.1.23 there is a change in the test kit"}, "lab_reports[1].result": {"input": "31", "base": 31}, "lab_reports[1].range": {"input": "19 - 49", "base": "19-49"}, "lab_reports[1].comment": {"input": "הערות לבדיקה: מיום 20.1.23 חל שינוי בשיטת הבדיקה. שים לב לשינוי בערכים המומלצים ובערכי הייחוס", "base": "מיום 20.1.23 חל שינוי בערכת הבדיקה. שים לב לשינוי בערכים המודדים וערכי הייחוס"}, "lab_reports[1].comment_english": {"input": "test note: as of 20.1.23, there has been a change in the test method. pay attention to the change in recommended and reference values.", "base": "from 20.1.23 there is a change in the test kit. pay attention to the change in measured values and reference values"}, "lab_reports[2].result": {"input": "0.74", "base": 0.74}, "lab_reports[2].range": {"input": "0.5 - 1", "base": "0.5-1"}, "lab_reports[2].comment": {"input": "הערות לבדיקה: מיום 20.1.23 חל שינוי בערכת הבדיקה. שים לב לשינוי בערכי הייחוס", "base": "מיום 20.1.23 חל שינוי בערכת הבדיקה. שים לב לשינוי בערכי הייחוס"}, "lab_reports[2].comment_english": {"input": "test note: as of 20.1.23, there has been a change in the test kit. pay attention to the change in reference values.", "base": "from 20.1.23 there is a change in the test kit. pay attention to the change in reference values"}, "lab_reports[3].result": {"input": "94", "base": 94}, "lab_reports[3].range": {"input": ">60", "base": null}, "lab_reports[3].comment": {"input": "הערות לבדיקה: ערך רצוי מעל 60.", "base": "ערך רצוי מעל 60."}, "lab_reports[3].comment_english": {"input": "test note: desired value above 60.", "base": "desirable value above 60."}, "lab_reports[4].result": {"input": "4", "base": 4}, "lab_reports[4].range": {"input": "3.5 - 5.1", "base": "3.5-5.1"}, "lab_reports[4].comment": {"input": "הערות לבדיקה: מיום 20.1.23 חל שינוי בערכת הבדיקה", "base": "מיום 20.1.23 חל שינוי בערכת הבדיקה"}, "lab_reports[4].comment_english": {"input": "test note: as of 20.1.23, there has been a change in the test kit.", "base": "from 20.1.23 there is a change in the test kit"}, "lab_reports[5].result": {"input": "139", "base": 139}, "lab_reports[5].range": {"input": "136 - 146", "base": "136-146"}, "lab_reports[5].comment": {"input": "הערות לבדיקה: מיום 20.1.23 חל שינוי בערכת הבדיקה", "base": "מיום 20.1.23 חל שינוי בערכת הבדיקה"}, "lab_reports[5].comment_english": {"input": "test note: as of 20.1.23, there has been a change in the test kit.", "base": "from 20.1.23 there is a change in the test kit"}, "lab_reports[6].result": {"input": "57", "base": 57}, "lab_reports[6].range": {"input": "30 - 120", "base": "30-120"}, "lab_reports[6].comment": {"input": "הערות לבדיקה: מיום 20.1.23 חל שינוי בערכת הבדיקה", "base": "מיום 20.1.23 חל שינוי בערכת הבדיקה"}, "lab_reports[6].comment_english": {"input": "test note: as of 20.1.23, there has been a change in the test kit.", "base": "from 20.1.23 there is a change in the test kit"}, "lab_reports[7].result": {"input": "11", "base": 11}, "lab_reports[7].range": {"input": "0 - 31", "base": "0-31"}, "lab_reports[7].comment": {"input": "הערות לבדיקה: מיום 20.1.23 חל שינוי בערכת הבדיקה", "base": "מיום 20.1.23 חל שינוי בערכת הבדיקה"}, "lab_reports[7].comment_english": {"input": "test note: as of 20.1.23, there has been a change in the test kit.", "base": "from 20.1.23 there is a change in the test kit"}, "lab_reports[8].result": {"input": "17", "base": 17}, "lab_reports[8].range": {"input": "10 - 31", "base": "10-31"}, "lab_reports[8].comment": {"input": "הערות לבדיקה: תחום 20.1.23 חל שינוי בערכת הבדיקה", "base": "תרום 20.1.23 חל שינוי בערכת הבדיקה"}, "lab_reports[8].comment_english": {"input": "test note: as of 20.1.23, there has been a change in the test kit.", "base": "from 20.1.23 there is a change in the test kit"}, "lab_reports[9].result": {"input": "88", "base": 88}, "lab_reports[9].range": {"input": "50 - 170", "base": "50-170"}, "lab_reports[9].comment": {"input": "הערות לבדיקה: תחום 22.3.23 חל שינוי בערכת הבדיקה. שים לב לשינוי בערכי הייחוס.", "base": "מיום 22.3.23 חל שינוי בערכת הבדיקה. שים לב לשינוי בערכי הייחוס."}, "lab_reports[9].comment_english": {"input": "test note: as of 22.3.23, there has been a change in the test kit. pay attention to the change in reference values.", "base": "from 22.3.23 there is a change in the test kit. pay attention to the change in reference values."}, "lab_reports[10].result": {"input": "34", "base": 34}, "lab_reports[10].range": {"input": "10 - 291", "base": "10-291"}, "lab_reports[10].comment": {"input": "הערות לבדיקה: תחום 3.4.23 חל שינוי בשיטת הבדיקה. שים לב לשינוי בערכי הייחוס.", "base": "מיום 3.4.23 חל שינוי בערכות הבדיקה. שים לב לשינוי בערכי הייחוס."}, "lab_reports[10].comment_english": {"input": "test note: as of 3.4.23, there has been a change in the test method. pay attention to the change in reference values.", "base": "from 3.4.23 there is a change in the test kits. pay attention to the change in reference values."}, "lab_reports[11].result": {"input": "10", "base": 10}, "lab_reports[11].comment": {"input": "הערות לבדיקה: ער<PERSON><PERSON> חיווי: נורמה גדול מ-5.4 ng/ml נמוך: 3.4 - 5.4 ng/ml", "base": "ערכי ייחוס: נורמה: גדול מ-5.4 ng/ml גבולי: 3.4 - 5.4 ng/ml"}, "lab_reports[11].comment_english": {"input": "test note: indicator values: normal greater than 5.4 ng/ml, low: 3.4 - 5.4 ng/ml", "base": "reference values: normal: greater than 5.4 ng/ml borderline: 3.4 - 5.4 ng/ml"}, "lab_reports[12].result": {"input": "6.3", "base": 6.3}, "lab_reports[12].range": {"input": "4.5 - 11", "base": "4.5-11"}, "lab_reports[12].units": {"input": "10*3/micl", "base": "10^3/micl"}, "lab_reports[13].result": {"input": "4.4", "base": 4.4}, "lab_reports[13].range": {"input": "4 - 5.2", "base": "4-5.2"}, "lab_reports[13].units": {"input": "10*6/micl", "base": "10^6/micl"}, "lab_reports[14].result": {"input": "13.8", "base": 13.8}, "lab_reports[14].range": {"input": "12.5 - 16", "base": "12.5-16"}, "lab_reports[15].result": {"input": "40.1", "base": 40.1}, "lab_reports[15].range": {"input": "36 - 46", "base": "36-46"}, "lab_reports[16].result": {"input": "91", "base": 91}, "lab_reports[16].range": {"input": "79 - 97", "base": "79-97"}, "lab_reports[17].result": {"input": "31.4", "base": 31.4}, "lab_reports[17].range": {"input": "27 - 34", "base": "27-34"}, "lab_reports[18].result": {"input": "34.4", "base": 34.4}, "lab_reports[18].range": {"input": "31 - 36", "base": "31-36"}, "lab_reports[19].result": {"input": "12.8", "base": 12.8}, "lab_reports[19].range": {"input": "11.6 - 15", "base": "11.6-15"}, "lab_reports[20].result": {"input": "218", "base": 218}, "lab_reports[20].range": {"input": "150 - 450", "base": "150-450"}, "lab_reports[21].result": {"input": "10", "base": 10}, "lab_reports[21].range": {"input": "8.5 - 12.9", "base": "8.5-12.9"}, "lab_reports[22].result": {"input": "35.9", "base": 35.9}, "lab_reports[22].range": {"input": "40 - 75", "base": "40-75"}, "lab_reports[23].result": {"input": "53.7", "base": 53.7}, "lab_reports[23].range": {"input": "22 - 43", "base": "22-43"}, "lab_reports[24].result": {"input": "7.2", "base": 7.2}, "lab_reports[24].range": {"input": "3 - 13", "base": "3-13"}, "lab_reports[25].result": {"input": "2.7", "base": 2.7}, "lab_reports[25].range": {"input": "0 - 6", "base": "0-6"}, "lab_reports[26].result": {"input": "0.5", "base": 0.5}, "lab_reports[26].range": {"input": "0 - 2", "base": "0-2"}, "lab_reports[27].result": {"input": "2.25", "base": 2.25}, "lab_reports[27].range": {"input": "1.8 - 7.7", "base": "1.8-7.7"}, "lab_reports[28].result": {"input": "3.36", "base": 3.36}, "lab_reports[28].range": {"input": "1 - 4.8", "base": "1-4.8"}, "lab_reports[29].result": {"input": "0.45", "base": 0.45}, "lab_reports[29].range": {"input": "0 - 1.1", "base": "0-1.1"}, "lab_reports[30].result": {"input": "0.17", "base": 0.17}, "lab_reports[30].range": {"input": "0 - 0.6", "base": "0-0.6"}, "lab_reports[31].result": {"input": "0.03", "base": 0.03}, "lab_reports[31].range": {"input": "0 - 0.2", "base": "0-0.2"}, "lab_reports[32].result": {"input": "10.3", "base": 10.3}, "lab_reports[32].range": {"input": "9.5 - 11.3", "base": "9.5-11.3"}, "lab_reports[32].comment": {"input": "הערות לבדיקה: בבדיקות עם תסמונת אנטיפוספוליפידית (antiphospholipid syndrome) עלולה להיות הארכה של תוצאות pt (seconds) ו-inr.", "base": null}, "lab_reports[32].comment_english": {"input": "test note: in tests with antiphospholipid syndrome, there may be prolongation of pt (seconds) and inr results.", "base": null}, "lab_reports[33].result": {"input": "1", "base": 1}, "lab_reports[33].comment": {"input": "הערות לבדיקה: בבדיקות עם תסמונת אנטיפוספוליפידית (antiphospholipid syndrome) עלולה להיות הארכה של תוצאות pt (seconds) ו-inr.", "base": "נבדקים עם תסמונת אנטיפוספוליפידית (antiphospholipid syndrome) עלולה להיות הארכה של תוצאות pt (seconds) ו-inr."}, "lab_reports[33].comment_english": {"input": "test note: in tests with antiphospholipid syndrome, there may be prolongation of pt (seconds) and inr results.", "base": "patients with antiphospholipid syndrome may have prolonged pt (seconds) and inr results."}, "lab_reports[34].result": {"input": "23.5", "base": 23.5}, "lab_reports[34].range": {"input": "21.6 - 28.7", "base": "21.6-28.7"}, "lab_reports[35].result": {"input": "2.13", "base": 2.13}, "lab_reports[35].range": {"input": "0.5 - 4.8", "base": "0.5-4.8"}, "lab_reports[35].comment": {"input": "הערות לבדיקה: החל מתאריך 18.9.22 חל שינוי בערכת הבדיקה. שים לב לעדכון ערכי התיחום.", "base": "החל מתאריך 18.9.22 חל שינוי בערכת הבדיקה. שים לב לערכון ערכי הייחוס."}, "lab_reports[35].comment_english": {"input": "test note: as of 18.9.22, there has been a change in the test kit. pay attention to the updated reference values.", "base": "from 18.9.22 there is a change in the test kit. pay attention to the update in reference values."}}, "total_fields": 331, "match_percentage": 68.27794561933534}}]