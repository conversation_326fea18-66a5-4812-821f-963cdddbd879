{"doctors": [{"id": 36, "doctorName": "u", "title": "dr", "doctorLastName": ""}, {"id": 59, "doctorName": "<PERSON>", "title": "dr", "doctorLastName": ""}, {"id": 48, "doctorName": "kentang uwuh", "title": "dr", "doctorLastName": ""}, {"id": 4, "doctorName": "<PERSON><PERSON>", "title": "dr", "doctorLastName": ""}, {"id": 7, "doctorName": "aכג<PERSON><PERSON><PERSON><PERSON>", "title": "dr", "doctorLastName": ""}, {"id": 8, "doctorName": "a", "title": "dr", "doctorLastName": ""}, {"id": 9, "doctorName": "<PERSON><PERSON>", "title": "dr", "doctorLastName": ""}, {"id": 38, "doctorName": "kobi", "title": "dr", "doctorLastName": ""}, {"id": 13, "doctorName": "Dr. <PERSON><PERSON>", "title": "dr", "doctorLastName": ""}, {"id": 14, "doctorName": "Dr. <PERSON>", "title": "dr", "doctorLastName": ""}, {"id": 639, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 640, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 641, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 44, "doctorName": "Dr. <PERSON>", "title": "dr", "doctorLastName": ""}, {"id": 41, "doctorName": "fdwsfs", "title": "dr", "doctorLastName": ""}, {"id": 15, "doctorName": "sus", "title": "dr", "doctorLastName": ""}, {"id": 56, "doctorName": "<PERSON><PERSON>", "title": "dr", "doctorLastName": ""}, {"id": 54, "doctorName": "rttt", "title": "dr", "doctorLastName": ""}, {"id": 17, "doctorName": "kjkj", "title": "dr", "doctorLastName": ""}, {"id": 34, "doctorName": "<PERSON><PERSON>", "title": "dr", "doctorLastName": ""}, {"id": 645, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 35, "doctorName": "gdf", "title": "dr", "doctorLastName": ""}, {"id": 52, "doctorName": "ron", "title": "dr", "doctorLastName": ""}, {"id": 5, "doctorName": "<PERSON><PERSON>", "title": "dr", "doctorLastName": ""}, {"id": 16, "doctorName": "dsds", "title": "dr", "doctorLastName": ""}, {"id": 1, "doctorName": "<PERSON><PERSON><PERSON>", "title": "dr", "doctorLastName": ""}, {"id": 2, "doctorName": "<PERSON><PERSON>", "title": "dr", "doctorLastName": ""}, {"id": 3, "doctorName": "<PERSON><PERSON>", "title": "dr", "doctorLastName": ""}, {"id": 50, "doctorName": "ro", "title": "dr", "doctorLastName": ""}, {"id": 81, "doctorName": "MediBoard", "title": "dr", "doctorLastName": "Management"}, {"id": 61, "doctorName": "<PERSON>", "title": "dr", "doctorLastName": ""}, {"id": 45, "doctorName": "<PERSON><PERSON><PERSON>", "title": "dr", "doctorLastName": ""}, {"id": 55, "doctorName": "dd", "title": "dr", "doctorLastName": ""}, {"id": 62, "doctorName": "<PERSON>", "title": "dr", "doctorLastName": ""}, {"id": 76, "doctorName": "<PERSON> ", "title": "prof", "doctorLastName": "Blomberg"}, {"id": 77, "doctorName": "<PERSON>", "title": "dr", "doctorLastName": "<PERSON>"}, {"id": 80, "doctorName": "SBS", "title": "dr", "doctorLastName": "SBS"}, {"id": 642, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 84, "doctorName": "Ofir", "title": "dr", "doctorLastName": "Oron"}, {"id": 82, "doctorName": "MediBoard", "title": "prof", "doctorLastName": "Managment"}, {"id": 42, "doctorName": "<PERSON><PERSON>", "title": "prof", "doctorLastName": "<PERSON><PERSON>"}, {"id": 78, "doctorName": "Artiom", "title": "prof", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 643, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 644, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "<PERSON>n"}, {"id": 85, "doctorName": "Ofir", "title": "prof", "doctorLastName": "Oron"}, {"id": 86, "doctorName": "<PERSON>ן", "title": "prof", "doctorLastName": "בר לוי"}, {"id": 646, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1096, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1730, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 1027, "doctorName": "<PERSON><PERSON><PERSON>", "title": "ms", "doctorLastName": "Golan"}, {"id": 49, "doctorName": "tt", "title": "dr", "doctorLastName": "fdddd"}, {"id": 647, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Gat"}, {"id": 648, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 649, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 650, "doctorName": "Arie", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 651, "doctorName": "Arie", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 79, "doctorName": "<PERSON>", "title": "prof", "doctorLastName": "<PERSON>"}, {"id": 652, "doctorName": "Ariel", "title": "Dr.", "doctorLastName": "Banai"}, {"id": 653, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 654, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 655, "doctorName": "Aviv", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 657, "doctorName": "Boaz", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1738, "doctorName": "Alon ", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 658, "doctorName": "Boaz", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 261, "doctorName": "Ido", "title": "prof", "doctorLastName": "<PERSON>"}, {"id": 262, "doctorName": "<PERSON><PERSON> ", "title": "dr", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 263, "doctorName": "<PERSON><PERSON> ", "title": "dr", "doctorLastName": "Keidar"}, {"id": 659, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 660, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 661, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 662, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 663, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 664, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 665, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 666, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 667, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 668, "doctorName": "<PERSON><PERSON>", "title": "Ms.", "doctorLastName": "Mendoza"}, {"id": 669, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 670, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Gefen - Doron"}, {"id": 671, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 672, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 673, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Orion"}, {"id": 674, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "<PERSON>"}, {"id": 675, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON> <PERSON> <PERSON><PERSON>"}, {"id": 676, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON>"}, {"id": 677, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 678, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "S<PERSON><PERSON>"}, {"id": 679, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON>"}, {"id": 680, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 681, "doctorName": "<PERSON><PERSON><PERSON> (Udi)", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 682, "doctorName": "Eitan", "title": "Prof.", "doctorLastName": "Scapa"}, {"id": 1594, "doctorName": "Sami ", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON> "}, {"id": 683, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Brazovski"}, {"id": 684, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 685, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 686, "doctorName": "<PERSON><PERSON>e", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 687, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 688, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON>"}, {"id": 689, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 690, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 691, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 692, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 694, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Morag"}, {"id": 695, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 696, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON>"}, {"id": 697, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON>"}, {"id": 698, "doctorName": "Iris", "title": "Prof.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 699, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "Avivi"}, {"id": 700, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 701, "doctorName": "Izhak", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 702, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 992, "doctorName": "Edo", "title": "prof", "doctorLastName": "B<PERSON><PERSON>"}, {"id": 704, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 705, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 706, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 707, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 708, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 709, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 710, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 711, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "Alcalay"}, {"id": 712, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 713, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Tripto - Shkolnik"}, {"id": 714, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Fridenson - Tzuk"}, {"id": 715, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 716, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 717, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 718, "doctorName": "Maya", "title": "Ms.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 720, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 721, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 722, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "<PERSON>"}, {"id": 763, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1741, "doctorName": "<PERSON><PERSON><PERSON> ", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1118, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 723, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "Giladi"}, {"id": 724, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 725, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Birge"}, {"id": 726, "doctorName": "Mordechai", "title": "Dr.", "doctorLastName": "Himmelfarb"}, {"id": 727, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 728, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Prof.", "doctorLastName": "Stern"}, {"id": 729, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Prof.", "doctorLastName": "Bichacho"}, {"id": 730, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Prof.", "doctorLastName": "Ma<PERSON><PERSON><PERSON>"}, {"id": 731, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 733, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Troncoso"}, {"id": 734, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 735, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 736, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Moolsintong"}, {"id": 738, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Hruban"}, {"id": 739, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 740, "doctorName": "Reuven", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 741, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 742, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 743, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 744, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 745, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "Landsberg"}, {"id": 746, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 749, "doctorName": "Sam", "title": "Mr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 748, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Eldor"}, {"id": 750, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 751, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 752, "doctorName": "Segal", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 753, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 754, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Armon"}, {"id": 755, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 756, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Mittal"}, {"id": 757, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 759, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 760, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Ohayon"}, {"id": 761, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 762, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Mr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 804, "doctorName": "<PERSON>", "title": "Mr.", "doctorLastName": "Verubel"}, {"id": 732, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1397, "doctorName": "Ilya", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 737, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Romano"}, {"id": 764, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1725, "doctorName": "Einat", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 766, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Mordechai"}, {"id": 767, "doctorName": "Wenqing", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 768, "doctorName": "<PERSON><PERSON><PERSON> (Kobi)", "title": "Prof.", "doctorLastName": "Stav"}, {"id": 769, "doctorName": "<PERSON><PERSON><PERSON> (Kobi)", "title": "Prof.", "doctorLastName": "Stav"}, {"id": 770, "doctorName": "Yair", "title": "Mr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 771, "doctorName": "Yan", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 772, "doctorName": "Yoav", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 773, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 775, "doctorName": "<PERSON><PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 776, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "<PERSON>n"}, {"id": 777, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Musak"}, {"id": 778, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Gat"}, {"id": 779, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 780, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1577, "doctorName": "Boaz", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 782, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 783, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Morag"}, {"id": 784, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON>"}, {"id": 785, "doctorName": "Ilya", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 787, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Shaked"}, {"id": 788, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 789, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Arlozoro<PERSON><PERSON><PERSON><PERSON>"}, {"id": 790, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 791, "doctorName": "Yoav", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 792, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "<PERSON>n"}, {"id": 793, "doctorName": "Ana", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 794, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "Kesler"}, {"id": 795, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 796, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 797, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 798, "doctorName": "Boaz", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 799, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 800, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 801, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "M<PERSON>F<PERSON>den"}, {"id": 802, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 803, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1119, "doctorName": "Boaz", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1145, "doctorName": "Hospital", "title": "Dr.", "doctorLastName": "Doctor"}, {"id": 781, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 805, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Avior"}, {"id": 806, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 807, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 808, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 809, "doctorName": "Ido", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 810, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 811, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON>"}, {"id": 812, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "Avivi"}, {"id": 813, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 814, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 815, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 816, "doctorName": "Liat", "title": "Dr.", "doctorLastName": "Apel - Sarid"}, {"id": 817, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>rling"}, {"id": 818, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "Waisbourd"}, {"id": 819, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Prof.", "doctorLastName": "Ma<PERSON><PERSON><PERSON>"}, {"id": 820, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON>"}, {"id": 821, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Trivizki"}, {"id": 822, "doctorName": "<PERSON><PERSON>", "title": "Mr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 823, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 824, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 825, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 826, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Pilz-<PERSON><PERSON><PERSON>"}, {"id": 827, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 828, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 829, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 830, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 831, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 832, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 833, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Sold"}, {"id": 834, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "<PERSON>n"}, {"id": 835, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 836, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 837, "doctorName": "Ariel", "title": "Dr.", "doctorLastName": "Banai"}, {"id": 838, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 839, "doctorName": "Avshalom", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 840, "doctorName": "Boaz", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 841, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 842, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Landau - P<PERSON>"}, {"id": 843, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON>"}, {"id": 844, "doctorName": "Eitan", "title": "Prof.", "doctorLastName": "<PERSON>"}, {"id": 845, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1017, "doctorName": "<PERSON><PERSON>", "title": "dr", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 847, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "St<PERSON><PERSON>"}, {"id": 848, "doctorName": "Hofit", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 849, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 850, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 851, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 852, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Bar - Ilan"}, {"id": 853, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Shaked"}, {"id": 854, "doctorName": "Menachem", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 855, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "<PERSON>"}, {"id": 856, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 857, "doctorName": "<PERSON><PERSON><PERSON><PERSON> (Motti)", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 858, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "Shibolet"}, {"id": 859, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 860, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 861, "doctorName": "Shlomo", "title": "Dr.", "doctorLastName": "Segev"}, {"id": 863, "doctorName": "Yair", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 864, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 865, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 866, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 867, "doctorName": "Ada", "title": "Prof.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 868, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 869, "doctorName": "Arie", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 870, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1569, "doctorName": "ABC", "title": "Dr.", "doctorLastName": "DEF"}, {"id": 872, "doctorName": "Gad", "title": "Prof.", "doctorLastName": "Barkai"}, {"id": 873, "doctorName": "Gad", "title": "Prof.", "doctorLastName": "Barkai"}, {"id": 874, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "St<PERSON><PERSON>"}, {"id": 875, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 862, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Ms.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 877, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 878, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 879, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Zajicek"}, {"id": 880, "doctorName": "Mordechai", "title": "Prof.", "doctorLastName": "Goldenberg"}, {"id": 881, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Prof.", "doctorLastName": "Stern"}, {"id": 882, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 883, "doctorName": "Reuven", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 884, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Eldor"}, {"id": 885, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 886, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Armon"}, {"id": 887, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 888, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 889, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1120, "doctorName": "Shlomi", "title": "Prof.", "doctorLastName": "<PERSON>"}, {"id": 1130, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Elkalai"}, {"id": 1570, "doctorName": "QWE", "title": "Dr.", "doctorLastName": "ASD"}, {"id": 143, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "Sandals"}, {"id": 1734, "doctorName": "Sol", "title": "Dr.", "doctorLastName": "Amsalem"}, {"id": 1398, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "Shibolet"}, {"id": 915, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON><PERSON> "}, {"id": 1114, "doctorName": "<PERSON>l", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1131, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 907, "doctorName": "<PERSON><PERSON><PERSON>", "title": "dr", "doctorLastName": "<PERSON>"}, {"id": 1026, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 911, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 747, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1028, "doctorName": "<PERSON><PERSON><PERSON>", "title": "dr", "doctorLastName": "<PERSON><PERSON>"}, {"id": 912, "doctorName": "<PERSON>", "title": "dr", "doctorLastName": "<PERSON>l"}, {"id": 1029, "doctorName": "<PERSON><PERSON>", "title": "dr", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1641, "doctorName": "Osnat ", "title": "Dr.", "doctorLastName": "Grots"}, {"id": 923, "doctorName": "<PERSON>", "title": "mr", "doctorLastName": "<PERSON>"}, {"id": 965, "doctorName": "Culture", "title": "", "doctorLastName": "Tests"}, {"id": 765, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 924, "doctorName": "<PERSON>", "title": "prof", "doctorLastName": "Nice"}, {"id": 896, "doctorName": "shosh", "title": "miss", "doctorLastName": "<PERSON>"}, {"id": 922, "doctorName": "<PERSON>", "title": "prof", "doctorLastName": "<PERSON><PERSON>"}, {"id": 949, "doctorName": "<PERSON>", "title": "dr", "doctorLastName": "<PERSON><PERSON>"}, {"id": 950, "doctorName": "<PERSON>", "title": "prof", "doctorLastName": "<PERSON><PERSON>"}, {"id": 951, "doctorName": "<PERSON>", "title": "mr", "doctorLastName": "<PERSON><PERSON>"}, {"id": 952, "doctorName": "<PERSON><PERSON>", "title": "dr", "doctorLastName": "Testing"}, {"id": 954, "doctorName": "<PERSON><PERSON>", "title": "dr", "doctorLastName": "Ziv"}, {"id": 957, "doctorName": "<PERSON>", "title": "dr", "doctorLastName": "Blank"}, {"id": 958, "doctorName": "<PERSON>", "title": "mr", "doctorLastName": "Jhones"}, {"id": 959, "doctorName": "<PERSON>", "title": "prof", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 953, "doctorName": "<PERSON>", "title": "ms", "doctorLastName": "Dow"}, {"id": 960, "doctorName": "<PERSON>", "title": "dr", "doctorLastName": "<PERSON><PERSON>"}, {"id": 967, "doctorName": "<PERSON>", "title": "Mr.", "doctorLastName": "<PERSON><PERSON>connor"}, {"id": 325, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 940, "doctorName": "<PERSON><PERSON>", "title": "Mrs.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 978, "doctorName": "<PERSON>", "title": "dr", "doctorLastName": "<PERSON><PERSON>"}, {"id": 979, "doctorName": "<PERSON>", "title": "prof", "doctorLastName": "<PERSON><PERSON>"}, {"id": 980, "doctorName": "<PERSON>", "title": "ms", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1726, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 786, "doctorName": "<PERSON>", "title": "dr", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1058, "doctorName": "Shlomo", "title": "dr", "doctorLastName": "<PERSON>"}, {"id": 1059, "doctorName": "<PERSON>", "title": "dr", "doctorLastName": "Morag"}, {"id": 1060, "doctorName": "<PERSON>", "title": "dr", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1062, "doctorName": "<PERSON>", "title": "dr", "doctorLastName": "<PERSON>man"}, {"id": 1065, "doctorName": "Iris", "title": "prof", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1066, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "<PERSON>"}, {"id": 1067, "doctorName": "<PERSON>", "title": "Miss.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1586, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1406, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1057, "doctorName": "<PERSON> ", "title": "Prof.", "doctorLastName": "Kashuk "}, {"id": 1431, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON>"}, {"id": 1121, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1123, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON> "}, {"id": 1127, "doctorName": "Menachem", "title": "Prof.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1046, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Snir"}, {"id": 1134, "doctorName": "Alon", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1136, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1138, "doctorName": "Sil<PERSON><PERSON> ", "title": "Dr.", "doctorLastName": "Brill"}, {"id": 1140, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1142, "doctorName": "Maya", "title": "Ms.", "doctorLastName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"id": 1149, "doctorName": "Hospital", "title": "Dr.", "doctorLastName": "Doctor"}, {"id": 1056, "doctorName": "Musa", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1159, "doctorName": "Hen", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1157, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Toledano"}, {"id": 1161, "doctorName": "<PERSON><PERSON>", "title": "Mr.", "doctorLastName": "<PERSON>"}, {"id": 1163, "doctorName": "Menachem", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1173, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1175, "doctorName": "<PERSON>ira", "title": "Dr.", "doctorLastName": "Zloto"}, {"id": 1177, "doctorName": "Zoldan", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1187, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "G<PERSON><PERSON>-<PERSON><PERSON><PERSON>"}, {"id": 1189, "doctorName": "Hospital", "title": "Dr.", "doctorLastName": "Doctor"}, {"id": 1191, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1195, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1197, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Maoz"}, {"id": 1201, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Mari"}, {"id": 1203, "doctorName": "Itzhak", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1205, "doctorName": "Vladimir", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1207, "doctorName": "<PERSON><PERSON> ", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1146, "doctorName": "Hospital", "title": "Dr.", "doctorLastName": "doctor"}, {"id": 1188, "doctorName": "Hospital", "title": "Dr.", "doctorLastName": "Doctor"}, {"id": 1103, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1736, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "Hasdai"}, {"id": 53, "doctorName": "r t", "title": "Dr.", "doctorLastName": "rer"}, {"id": 51, "doctorName": "ron", "title": "Miss.", "doctorLastName": "ewer"}, {"id": 1176, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Giron"}, {"id": 1742, "doctorName": "Ruti", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1122, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 876, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 966, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "Bar"}, {"id": 1124, "doctorName": "Hen", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1128, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1133, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Bar-Ilan"}, {"id": 1135, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Feldbrin"}, {"id": 1137, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Morag"}, {"id": 1139, "doctorName": "Gad", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1147, "doctorName": "Hospital", "title": "Dr.", "doctorLastName": "Doctor"}, {"id": 1148, "doctorName": "Hospital", "title": "Dr.", "doctorLastName": "Doctor"}, {"id": 1158, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1160, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1156, "doctorName": "Osnat", "title": "Prof.", "doctorLastName": "Groutz"}, {"id": 1162, "doctorName": "Lilly", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON><PERSON> Naveh"}, {"id": 1164, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1174, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1190, "doctorName": "Yair", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1192, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1194, "doctorName": "<PERSON> ", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1196, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Ms.", "doctorLastName": "."}, {"id": 1198, "doctorName": "<PERSON><PERSON> ", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON> "}, {"id": 1204, "doctorName": "Shlomi ", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1206, "doctorName": "<PERSON>l", "title": "Dr.", "doctorLastName": "Yalon"}, {"id": 1209, "doctorName": "Menachem", "title": "Dr.", "doctorLastName": "<PERSON><PERSON> "}, {"id": 1210, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1211, "doctorName": "Or ", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1212, "doctorName": "<PERSON><PERSON><PERSON> ", "title": "Dr.", "doctorLastName": "Zidan "}, {"id": 1213, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Le<PERSON><PERSON>"}, {"id": 1215, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 1216, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1217, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1223, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1224, "doctorName": "Low", "title": "Dr.", "doctorLastName": "Right"}, {"id": 1237, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1238, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1247, "doctorName": "Daren", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1248, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Golan"}, {"id": 1229, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1230, "doctorName": "Valentina ", "title": "Dr.", "doctorLastName": "Zemser<PERSON><PERSON> "}, {"id": 1249, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1132, "doctorName": "Hospital", "title": "Dr.", "doctorLastName": "Doctor"}, {"id": 1208, "doctorName": "Hospital", "title": "Dr.", "doctorLastName": "doctor"}, {"id": 1231, "doctorName": "Hospital", "title": "Dr.", "doctorLastName": "Doctor"}, {"id": 1235, "doctorName": "Hospital", "title": "Dr.", "doctorLastName": "Doctor"}, {"id": 1250, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1239, "doctorName": "Boaz", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1240, "doctorName": "Menachem", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1581, "doctorName": "Osnat", "title": "Prof.", "doctorLastName": "Grotz"}, {"id": 57, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Bar-Shira"}, {"id": 1243, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1244, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Tripto"}, {"id": 43, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Eldor"}, {"id": 1272, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1241, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1246, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Logothetis"}, {"id": 1273, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>a"}, {"id": 1254, "doctorName": "Hospital", "title": "Dr.", "doctorLastName": "Doctor"}, {"id": 1255, "doctorName": "Hospital", "title": "Dr.", "doctorLastName": "Doctor"}, {"id": 1609, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Koenigs"}, {"id": 1399, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1275, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1274, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Tam"}, {"id": 1281, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1285, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1287, "doctorName": "Snir", "title": "Dr.", "doctorLastName": "Livne"}, {"id": 1288, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Menas"}, {"id": 1289, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "R<PERSON>vski"}, {"id": 1290, "doctorName": "Boaz", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1291, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 1292, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1293, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1286, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1284, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1294, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 1295, "doctorName": "Yulia ", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1296, "doctorName": "Hospital", "title": "Dr.", "doctorLastName": "Doctor"}, {"id": 1297, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Gilboa"}, {"id": 1572, "doctorName": "test", "title": "Prof.", "doctorLastName": "test"}, {"id": 1400, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1378, "doctorName": "Eve", "title": "Dr.", "doctorLastName": "Black"}, {"id": 1379, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1336, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1337, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "White"}, {"id": 1322, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1283, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1298, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON> "}, {"id": 1380, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1402, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Piklova"}, {"id": 1368, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1404, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1282, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1323, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Green"}, {"id": 1141, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1369, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1376, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Snir"}, {"id": 1377, "doctorName": "Hofit ", "title": "Dr.", "doctorLastName": "<PERSON> "}, {"id": 1381, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1408, "doctorName": "", "title": "", "doctorLastName": ""}, {"id": 1409, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1411, "doctorName": "Eitan", "title": "Prof.", "doctorLastName": "<PERSON>"}, {"id": 1410, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1403, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Decaprio"}, {"id": 1412, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Grey"}, {"id": 1413, "doctorName": "Boaz", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1414, "doctorName": "Boaz", "title": "Dr.", "doctorLastName": "<PERSON><PERSON> "}, {"id": 1425, "doctorName": "<PERSON><PERSON> ", "title": "Dr.", "doctorLastName": "Toledano "}, {"id": 1416, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1423, "doctorName": "<PERSON><PERSON> ", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON> "}, {"id": 1415, "doctorName": "<PERSON>na", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1424, "doctorName": "Osnat", "title": "Prof.", "doctorLastName": "Groutz"}, {"id": 1427, "doctorName": "<PERSON> ", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1426, "doctorName": "<PERSON> ", "title": "Dr.", "doctorLastName": "Nachtigall"}, {"id": 1428, "doctorName": "<PERSON>am ", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1429, "doctorName": "Mordechai ", "title": "Dr.", "doctorLastName": "Himmelfarb"}, {"id": 1430, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Brand"}, {"id": 1432, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1582, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1739, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 1595, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1598, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 1600, "doctorName": "<PERSON> ", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 1606, "doctorName": "Eden", "title": "Prof.", "doctorLastName": "<PERSON>"}, {"id": 1607, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1608, "doctorName": "Shlomo", "title": "Dr.", "doctorLastName": "Segev"}, {"id": 1610, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 1611, "doctorName": "Boaz", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1614, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"id": 1615, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Zaiac"}, {"id": 1616, "doctorName": "PJ", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1617, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "G<PERSON>"}, {"id": 1618, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1619, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1620, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1621, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1622, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1623, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 1624, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1632, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1625, "doctorName": "Rotem", "title": "Dr.", "doctorLastName": "Zadok"}, {"id": 758, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 656, "doctorName": "<PERSON>", "title": "Ms.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1628, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Luxford"}, {"id": 1630, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 913, "doctorName": "<PERSON>", "title": "Mr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1633, "doctorName": "<PERSON><PERSON><PERSON> ", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 719, "doctorName": "Menachem", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1634, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1640, "doctorName": "<PERSON><PERSON><PERSON> ", "title": "Dr.", "doctorLastName": "Feldbrin"}, {"id": 1642, "doctorName": "Mordechai ", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1663, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>man"}, {"id": 774, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1664, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>man"}, {"id": 1679, "doctorName": "Hagit", "title": "Dr.", "doctorLastName": "Shoufel-Havakok"}, {"id": 1680, "doctorName": "Ido", "title": "Dr.", "doctorLastName": "Avivi"}, {"id": 1434, "doctorName": "<PERSON><PERSON>", "title": "Ms.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1435, "doctorName": "<PERSON><PERSON>", "title": "Ms.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1436, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1437, "doctorName": "Hospital", "title": "Dr.", "doctorLastName": "Doctor"}, {"id": 1438, "doctorName": "<PERSON><PERSON> ", "title": "Dr.", "doctorLastName": "Mosek"}, {"id": 1439, "doctorName": "<PERSON><PERSON> ", "title": "Prof.", "doctorLastName": "Kesler"}, {"id": 1440, "doctorName": "Hen", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1452, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1453, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1454, "doctorName": "Salus", "title": "Dr.", "doctorLastName": "clinic doctor"}, {"id": 1458, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1459, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Roll"}, {"id": 1460, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1461, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1465, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 871, "doctorName": "Eitan", "title": "Prof.", "doctorLastName": "<PERSON>"}, {"id": 1478, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Biden"}, {"id": 1479, "doctorName": "Moore 2", "title": "Dr.", "doctorLastName": "House 2"}, {"id": 1480, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1481, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Monakier"}, {"id": 1068, "doctorName": "Mat", "title": "Prof.", "doctorLastName": "<PERSON>"}, {"id": 703, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 693, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1466, "doctorName": "<PERSON> ", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1485, "doctorName": "Maya", "title": "Ms.", "doctorLastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"id": 1486, "doctorName": "Maya", "title": "Ms.", "doctorLastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"id": 1488, "doctorName": "Shlomo", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1489, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1490, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "Jais"}, {"id": 1493, "doctorName": "Tzipi", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1496, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON> "}, {"id": 1482, "doctorName": "<PERSON><PERSON>", "title": "Mrs.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1497, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1506, "doctorName": "<PERSON>", "title": "Ms.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1498, "doctorName": "Ovadia", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1505, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1455, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1492, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1456, "doctorName": "Sami", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1495, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1596, "doctorName": "<PERSON> ", "title": "Dr.", "doctorLastName": "<PERSON> "}, {"id": 1599, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1457, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Silver"}, {"id": 1494, "doctorName": "Arik", "title": "Dr.", "doctorLastName": "Alper"}, {"id": 1510, "doctorName": "Green ", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1537, "doctorName": "<PERSON> ", "title": "Dr.", "doctorLastName": "A Joice"}, {"id": 1539, "doctorName": "Benford", "title": "Dr.", "doctorLastName": "Normal"}, {"id": 1045, "doctorName": "Alon", "title": "Prof.", "doctorLastName": "Barsheshet"}, {"id": 1515, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1516, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "Win"}, {"id": 1584, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1518, "doctorName": "<PERSON> ", "title": "Dr.", "doctorLastName": "huggins"}, {"id": 1519, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1523, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1107, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1540, "doctorName": "<PERSON><PERSON><PERSON> ", "title": "Prof.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1597, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON> "}, {"id": 1527, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1373, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1529, "doctorName": "nm", "title": "Prof.", "doctorLastName": "mk"}, {"id": 1531, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1530, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1547, "doctorName": "mn", "title": "Prof.", "doctorLastName": "jjn"}, {"id": 1563, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Wated"}, {"id": 1536, "doctorName": "<PERSON> ", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1549, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1532, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1550, "doctorName": "<PERSON> ", "title": "Ms.", "doctorLastName": "Menz"}, {"id": 1551, "doctorName": "<PERSON> ", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1552, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1553, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1555, "doctorName": "mati", "title": "Prof.", "doctorLastName": "dor"}, {"id": 1557, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "<PERSON>"}, {"id": 1558, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1559, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "<PERSON>"}, {"id": 1564, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Wated"}, {"id": 1018, "doctorName": "Maya", "title": "Ms.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 846, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Milloh - Raz"}, {"id": 1682, "doctorName": "Maya", "title": "Ms.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1686, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Goshen"}, {"id": 1689, "doctorName": "Boaz", "title": "Dr.", "doctorLastName": "T<PERSON><PERSON>"}, {"id": 1690, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Forschmidt"}, {"id": 1691, "doctorName": "Oded", "title": "Dr.", "doctorLastName": "Sagiv"}, {"id": 1692, "doctorName": "Alon", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1693, "doctorName": "<PERSON><PERSON><PERSON> ", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1694, "doctorName": "Boaz", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1695, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1698, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1702, "doctorName": "Orit", "title": "Dr.", "doctorLastName": "Golan"}, {"id": 1703, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "R<PERSON>vski"}, {"id": 1704, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Morag"}, {"id": 1705, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1708, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1709, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Gilboa"}, {"id": 1710, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1711, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1712, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1713, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON> "}, {"id": 1714, "doctorName": "Avi<PERSON> ", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1715, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "Eldar"}, {"id": 1716, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Basin"}, {"id": 1717, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1719, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON> "}, {"id": 1729, "doctorName": "Carmel", "title": "Dr.", "doctorLastName": "Avshalom"}, {"id": 1737, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1740, "doctorName": "Florence Anne <PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1744, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Almog"}, {"id": 1745, "doctorName": "Ruti", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1747, "doctorName": "Libi", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1748, "doctorName": "Ada", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1749, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "<PERSON>"}, {"id": 1751, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1752, "doctorName": "Luba", "title": "Dr.", "doctorLastName": "Bar Saf"}, {"id": 1753, "doctorName": "Eitan ", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1755, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Ms.", "doctorLastName": "Binder"}, {"id": 1756, "doctorName": "<PERSON>.", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1758, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1754, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"id": 1759, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1750, "doctorName": "Hospital", "title": "Dr.", "doctorLastName": "doctor"}, {"id": 1760, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": " Salai"}, {"id": 1763, "doctorName": "Hospital", "title": "Dr.", "doctorLastName": "Doctor"}, {"id": 1764, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1765, "doctorName": "Tsvi", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 1766, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1761, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Liberty"}, {"id": 1785, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1786, "doctorName": "<PERSON><PERSON><PERSON> ", "title": "Dr.", "doctorLastName": "Feldbrin"}, {"id": 1787, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1788, "doctorName": "Sonya ", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1790, "doctorName": "Netanel ", "title": "Dr.", "doctorLastName": "Orenstein"}, {"id": 1791, "doctorName": "Marina ", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1792, "doctorName": "Shlomi", "title": "Prof.", "doctorLastName": "<PERSON>"}, {"id": 1793, "doctorName": "Marina", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1797, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1798, "doctorName": "<PERSON><PERSON> ", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON> "}, {"id": 1802, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Milloh-Raz"}, {"id": 1803, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1801, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1804, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1805, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1806, "doctorName": "Itzhak ", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1807, "doctorName": "Shlomi", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1808, "doctorName": "Boaz", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1809, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON>"}, {"id": 1810, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1811, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1812, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 1813, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1762, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Morag"}, {"id": 1789, "doctorName": "Natalie ", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1815, "doctorName": "<PERSON> ", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1767, "doctorName": "Rodica", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 1823, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": " <PERSON>"}, {"id": 1825, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON>"}, {"id": 1826, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1827, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1757, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1828, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1829, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1831, "doctorName": "trst", "title": "Dr.", "doctorLastName": "dr"}, {"id": 1832, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1833, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Markado"}, {"id": 1834, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>v<PERSON>"}, {"id": 1835, "doctorName": "Maya", "title": "Ms.", "doctorLastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"id": 1836, "doctorName": " Golanz", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1837, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Rhodes"}, {"id": 1838, "doctorName": "<PERSON><PERSON>", "title": "Mr.", "doctorLastName": "Sahar"}, {"id": 1839, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>vski"}, {"id": 1840, "doctorName": "Netanel", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1824, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1842, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1843, "doctorName": "Hospital", "title": "Dr.", "doctorLastName": "Doctor"}, {"id": 1845, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1847, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Lessing"}, {"id": 1848, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Ginsburg"}, {"id": 1849, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1851, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1868, "doctorName": "<PERSON> ", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1854, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1855, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1865, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 1866, "doctorName": "Hospital", "title": "Dr.", "doctorLastName": "Doctor"}, {"id": 1869, "doctorName": "Ziv", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1870, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1871, "doctorName": " <PERSON><PERSON><PERSON><PERSON> ", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1872, "doctorName": "<PERSON><PERSON> ", "title": "Dr.", "doctorLastName": "Rosenz<PERSON>g"}, {"id": 1873, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Maoz"}, {"id": 1874, "doctorName": "Iris", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1876, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Or"}, {"id": 1877, "doctorName": "<PERSON> ", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1880, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1881, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1882, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"id": 1883, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Bar-Shai"}, {"id": 1884, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Mittal"}, {"id": 1885, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1886, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1887, "doctorName": "fi<PERSON>i", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1889, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Prof.", "doctorLastName": "Mother"}, {"id": 1888, "doctorName": "Adwo<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>h"}, {"id": 1891, "doctorName": "Yan", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1395, "doctorName": "rotem", "title": "Dr.", "doctorLastName": "zadok"}, {"id": 1892, "doctorName": "dan", "title": "Dr.", "doctorLastName": "dan"}, {"id": 1893, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Morag"}, {"id": 1894, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"id": 1895, "doctorName": "Hospital", "title": "Dr.", "doctorLastName": "Doctor"}, {"id": 1896, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Basin"}, {"id": 1897, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Mills"}, {"id": 1898, "doctorName": "<PERSON><PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1899, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1902, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1903, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Basel"}, {"id": 1904, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1905, "doctorName": "<PERSON><PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 1906, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON> Bach"}, {"id": 1907, "doctorName": "<PERSON><PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1908, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1909, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1910, "doctorName": "Boaz", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1911, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1912, "doctorName": "Hospital", "title": "Dr.", "doctorLastName": "Doctor"}, {"id": 1913, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1914, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1915, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": " <PERSON><PERSON><PERSON>"}, {"id": 1916, "doctorName": "<PERSON> ", "title": "Dr.", "doctorLastName": "Morag"}, {"id": 1917, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1918, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1919, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "Bar-Ilan"}, {"id": 1920, "doctorName": "Ariel", "title": "Prof.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1921, "doctorName": "Regina", "title": "Dr.", "doctorLastName": "Agizim"}, {"id": 1901, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1922, "doctorName": "<PERSON>", "title": "Prof.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1923, "doctorName": "Luba", "title": "Dr.", "doctorLastName": "Tao"}, {"id": 1924, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "Bar-Shai"}, {"id": 1925, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1879, "doctorName": "<PERSON><PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON><PERSON>"}, {"id": 1926, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1928, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON>"}, {"id": 1929, "doctorName": "<PERSON>", "title": "Dr.", "doctorLastName": "<PERSON><PERSON>"}, {"id": 1930, "doctorName": "Tzvi", "title": "Dr.", "doctorLastName": "<PERSON>l"}], "institutions": [{"id": 1, "value": "example_value", "displayName": "Example Institute"}, {"id": 18, "value": "Directory of Nursing Homes in Israel", "displayName": "Directory of Nursing Homes in Israel"}, {"id": 21, "value": "Emek Medical Center (Ha-Emek hospital in Afula)", "displayName": "Emek Medical Center (Ha-Emek hospital in Afula)"}, {"id": 25, "value": "Harzfeld Geriatric Medical Center ", "displayName": "Harzfeld Geriatric Medical Center "}, {"id": 29, "value": "Kaplan Medical Center", "displayName": "Kaplan Medical Center"}, {"id": 33, "value": "Lowenstein Hospital – Rehabilitation Center", "displayName": "Lowenstein Hospital – Rehabilitation Center"}, {"id": 35, "value": "Mayanei Hayeshua Medical Center", "displayName": "Mayanei Hayeshua Medical Center"}, {"id": 36, "value": "<PERSON><PERSON>", "displayName": "<PERSON><PERSON>"}, {"id": 46, "value": "<PERSON><PERSON><PERSON> Mental Health Center ", "displayName": "<PERSON><PERSON><PERSON> Mental Health Center "}, {"id": 51, "value": "Shoham Medical Center (Neve Avot)", "displayName": "Shoham Medical Center (Neve Avot)"}, {"id": 55, "value": "Yoseftal Medical Center (Yoseftal Hospital Eilat)", "displayName": "Yoseftal Medical Center (Yoseftal Hospital Eilat)"}, {"id": 11, "value": " Barzilai Medical Center", "displayName": " Barzilai Medical Center"}, {"id": 70, "value": "Hospital Paris Saint-Joseph", "displayName": "Hospital Paris Saint-Joseph"}, {"id": 40, "value": "The National Institute of Neuropsychological Rehabilitation", "displayName": "The National Institute of Neuropsychological Rehabilitation"}, {"id": 69, "value": "Suny Upstate Medical University Hospital", "displayName": "Suny Upstate Medical University Hospital"}, {"id": 4, "value": "The Israeli Alzheimer Medical Center", "displayName": "The Israeli Alzheimer Medical Center"}, {"id": 56, "value": "The <PERSON> Sieff hospital (Ziv Hospital)", "displayName": "The <PERSON> Sieff hospital (Ziv Hospital)"}, {"id": 172, "value": "Medica", "displayName": ""}, {"id": 31, "value": "Leumit Health Survices", "displayName": ""}, {"id": 60, "value": "Washington Hospital", "displayName": "Washington Hospital"}, {"id": 61, "value": "Oklahoma Proton Center", "displayName": "Oklahoma Proton Center"}, {"id": 62, "value": "Mount Sinai Hospital", "displayName": "Mount Sinai Hospital"}, {"id": 63, "value": "Mayo Clinic", "displayName": "Mayo Clinic"}, {"id": 64, "value": "The Johns Hopkins Hospital", "displayName": "The Johns Hopkins Hospital"}, {"id": 67, "value": "Queens Hospital Center", "displayName": "Queens Hospital Center"}, {"id": 68, "value": "Jackson Memorial Hospital", "displayName": "Jackson Memorial Hospital"}, {"id": 71, "value": "Lenox Hill Hospital", "displayName": "Lenox Hill Hospital"}, {"id": 72, "value": "NewYork-Presbyterian Hospital", "displayName": "NewYork-Presbyterian Hospital"}, {"id": 75, "value": "Walter Reed Military Hospital", "displayName": "Walter Reed Military Hospital"}, {"id": 76, "value": "Jamaica Hospital Medical Center", "displayName": "Jamaica Hospital Medical Center"}, {"id": 77, "value": "<PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 9, "value": "Assuta Rishon Lezion", "displayName": "Assuta Rishon Lezion"}, {"id": 26, "value": "Herzliya Medical Center", "displayName": "Herzliya Medical Center"}, {"id": 57, "value": "Maccabi Healthcare Services", "displayName": "Maccabi Healthcare Services"}, {"id": 38, "value": "Meuhedet Health Services", "displayName": "Meuhedet Health Services"}, {"id": 42, "value": "Ramat-Aviv Medical Center", "displayName": "Ramat-Aviv Medical Center"}, {"id": 45, "value": "Schneider Children's Medical Center", "displayName": "Schneider Children's Medical Center"}, {"id": 53, "value": "Tel Aviv Sourasky Medical Center (Ichilov)", "displayName": "Tel Aviv Sourasky Medical Center (Ichilov)"}, {"id": 7, "value": "<PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 43, "value": "Rambam Medical Center", "displayName": "Rambam Medical Center"}, {"id": 47, "value": "<PERSON><PERSON><PERSON> Medical Center", "displayName": "<PERSON><PERSON><PERSON> Medical Center"}, {"id": 5, "value": "Assuta Hospital Beer-Sheba", "displayName": "Assuta Hospital Beer-Sheba"}, {"id": 2, "value": "Abarbanel Mental Health Center ", "displayName": "Abarbanel Mental Health Center "}, {"id": 6, "value": "Assuta Hospital Haifa", "displayName": "Assuta Hospital Haifa"}, {"id": 13, "value": "Bnai Zion Medical Center ", "displayName": "Bnai Zion Medical Center "}, {"id": 79, "value": "University of Washington  Medical Center", "displayName": "UW Medical Center"}, {"id": 80, "value": "Fred Hutchinson Cancer Center", "displayName": "Fred Hutchinson Cancer Center"}, {"id": 83, "value": "Private clinic", "displayName": "Private clinic"}, {"id": 84, "value": "Ein Tal Medical Center", "displayName": "Ein Tal Medical Center"}, {"id": 86, "value": "Salus", "displayName": "Salus "}, {"id": 15, "value": "Sheba Medical Center (Tel Hashomer)", "displayName": "Sheba Medical Center (Tel Hashomer)"}, {"id": 87, "value": "Beit Gedi Medical Center", "displayName": "Beit Gedi Medical Center"}, {"id": 88, "value": "Genova Diagnostics", "displayName": "Genova Diagnostics"}, {"id": 89, "value": "Sollis Health", "displayName": "Sollis Health"}, {"id": 90, "value": "Centre Hospitalier Universitaire (CHU) de Bordeaux", "displayName": "Centre Hospitalier Universitaire de Bordeaux"}, {"id": 92, "value": "Mediton", "displayName": "Mediton"}, {"id": 93, "value": "Boston Heart Diagnostics", "displayName": "Boston Heart Diagnostics"}, {"id": 94, "value": "New Rambam Laboratories", "displayName": "New Rambam Laboratories"}, {"id": 95, "value": "Schweiger Dermatology Group – Murray Hill, New York", "displayName": "Schweiger Dermatology Group – Murray Hill, New York"}, {"id": 24, "value": "Hadassah Hospital", "displayName": "Hadassah Hospital"}, {"id": 96, "value": "Diagnostic Solutions Laboratory", "displayName": "Diagnostic Solutions Laboratory"}, {"id": 97, "value": "Raphael Hospital", "displayName": "Raphael Hospital"}, {"id": 106, "value": "Chhp London", "displayName": "Chhp London"}, {"id": 99, "value": "American Academy of Dermatology", "displayName": "American Academy of Dermatology"}, {"id": 82, "value": "Gna Medical", "displayName": "Gna Medical"}, {"id": 117, "value": "H.Clinic", "displayName": "H.Clinic"}, {"id": 91, "value": "Hospital Alianca ", "displayName": "Hospital Alianca "}, {"id": 8, "value": "<PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 103, "value": "Bichacho Clinic", "displayName": "Bichacho Clinic"}, {"id": 104, "value": "Biosfer Teslab", "displayName": "Biosfer Teslab"}, {"id": 121, "value": "Imacs - Israeli Motion Analysis Center for Sports", "displayName": "Imacs - Israeli Motion Analysis Center for Sports"}, {"id": 102, "value": "<PERSON><PERSON><PERSON> (Advanced Orthopedic Center )", "displayName": "<PERSON><PERSON><PERSON> (Advanced Orthopedic Center )"}, {"id": 101, "value": "<PERSON><PERSON><PERSON> (Assia Medical)", "displayName": "<PERSON><PERSON><PERSON> (Assia Medical)"}, {"id": 100, "value": "<PERSON><PERSON><PERSON> (A.R.M Medical Center)", "displayName": "<PERSON><PERSON><PERSON> (A.R.M Medical Center)"}, {"id": 123, "value": "Jmedical", "displayName": "Jmedical"}, {"id": 107, "value": "Cornell Medical Center", "displayName": "Cornell Medical Center"}, {"id": 108, "value": "DayTwo", "displayName": "DayTwo"}, {"id": 109, "value": "Dianon Pathology", "displayName": "Dianon Pathology"}, {"id": 110, "value": "Eco Lab", "displayName": "Eco Lab"}, {"id": 19, "value": "Edith Wolfson Medical Center", "displayName": "Edith Wolfson Medical Center"}, {"id": 111, "value": "Eyecon", "displayName": "Eyecon"}, {"id": 126, "value": "Yair <PERSON> Center", "displayName": "Yair <PERSON> Center"}, {"id": 85, "value": "Nyu Langone Health", "displayName": "Nyu Langone Health"}, {"id": 81, "value": "Phoenix Veterans Affairs Hospital", "displayName": "Phoenix Veterans Affairs Hospital"}, {"id": 116, "value": "Griffin Hospital", "displayName": "Griffin Hospital"}, {"id": 118, "value": "Hearing Associates, Inc.", "displayName": "Hearing Associates, Inc."}, {"id": 28, "value": "Hillel Yaffe Medical Center", "displayName": "Hillel Yaffe Medical Center"}, {"id": 120, "value": "House Ear Clinic", "displayName": "House Ear Clinic"}, {"id": 122, "value": " Immufood Lab", "displayName": " Immufood Lab"}, {"id": 125, "value": "Kyto Meridien Lab", "displayName": "Kyto Meridien Lab"}, {"id": 127, "value": "Life Length", "displayName": "Life Length"}, {"id": 128, "value": "<PERSON><PERSON>", "displayName": "<PERSON><PERSON>"}, {"id": 129, "value": "Massachusetts General Hospital", "displayName": "Massachusetts General Hospital"}, {"id": 132, "value": "Medical Corps (Israel)", "displayName": "Medical Corps (Israel)"}, {"id": 20, "value": "Elisha Medical Center (Elisha Hospital)", "displayName": "Elisha Medical Center (Elisha Hospital)"}, {"id": 133, "value": "Memorial Hospital for Cancer and Allied Diseases", "displayName": "Memorial Hospital for Cancer and Allied Diseases"}, {"id": 134, "value": "Memorial Sloan Kettering Cancer Center", "displayName": "Memorial Sloan Kettering Cancer Center"}, {"id": 135, "value": "Metabolic Lab", "displayName": "Metabolic Lab"}, {"id": 136, "value": "Quest Diagnostics", "displayName": "Quest Diagnostics"}, {"id": 137, "value": "Cedars-Sinai Medical Center", "displayName": "Cedars-Sinai Medical Center"}, {"id": 22, "value": "Fliman Geriatric Center", "displayName": "Fliman Geriatric Center"}, {"id": 23, "value": "Geha Mental Health Center", "displayName": "Geha Mental Health Center"}, {"id": 141, "value": "Patho-Lab Diagnostics ", "displayName": "Patho-Lab Diagnostics "}, {"id": 142, "value": "Princess Margaret Cancer Centre", "displayName": "Princess Margaret Cancer Centre"}, {"id": 143, "value": "<PERSON><PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 41, "value": "Rabin Medical Center (Beilinson and HaSharon Hospitals)", "displayName": "Rabin Medical Center (Beilinson and HaSharon Hospitals)"}, {"id": 44, "value": "Reuth TLV Rehabilitation Hospital", "displayName": "Reuth TLV Rehabilitation Hospital"}, {"id": 144, "value": "Schwartz-Arad Surgical Center", "displayName": "Schwartz-Arad Surgical Center"}, {"id": 49, "value": "Shamir Medical Center (Assaf <PERSON>) ", "displayName": "Shamir Medical Center (Assaf <PERSON>) "}, {"id": 27, "value": "Herzog Hospital", "displayName": "Herzog Hospital"}, {"id": 147, "value": "Sportopedia", "displayName": "Sportopedia"}, {"id": 148, "value": "The Great Plains Laboratory", "displayName": "The Great Plains Laboratory"}, {"id": 149, "value": "Thyro Clinic", "displayName": "Thyro Clinic"}, {"id": 150, "value": "TLV Medical center", "displayName": "TLV Medical center"}, {"id": 152, "value": "Wingate Institute", "displayName": "Wingate Institute"}, {"id": 37, "value": "Meir Medical Center ", "displayName": "Meir Medical Center "}, {"id": 3, "value": "Alyn Hospital  (Pediatric and Adolescent Rehabilitation Center)", "displayName": "yn Hospital  (Pediatric and Adolescent Rehabilitation Center)"}, {"id": 16, "value": "Clalit Health Services", "displayName": "Clalit Health Services"}, {"id": 162, "value": "Hospital for Special Surgery (HSS)", "displayName": "Hospital for Special Surgery (HSS)"}, {"id": 131, "value": "Mdc Medical Center", "displayName": "Mdc Medical Center"}, {"id": 169, "value": "Mosa", "displayName": "Mosa"}, {"id": 139, "value": "Ofek Plus", "displayName": "Ofek Plus"}, {"id": 140, "value": "Optic Center (Vrovel)", "displayName": "Optic Center (Vrovel)"}, {"id": 161, "value": "Ramat <PERSON>pe Hospital", "displayName": "Ramat <PERSON>pe Hospital"}, {"id": 154, "value": "Cleveland Clinic", "displayName": "Cleveland Clinic"}, {"id": 155, "value": "Greater Miami Skin & Laser Center", "displayName": ""}, {"id": 156, "value": "Bayside Ambulatory Center", "displayName": ""}, {"id": 157, "value": "Baptist Health Surgery Center", "displayName": ""}, {"id": 158, "value": "TEST ONLY", "displayName": "TEST ONLY"}, {"id": 159, "value": "University of Nebraska Medical Center", "displayName": "University of Nebraska Medical Center"}, {"id": 160, "value": "Catholic Health Care West", "displayName": ""}, {"id": 163, "value": "Sarasota Memorial Hospital", "displayName": "Sarasota Memorial Hospital"}, {"id": 165, "value": "Sharlin Dental Clinic", "displayName": "Sharlin Dental Clinic"}, {"id": 166, "value": "<PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON>"}, {"id": 145, "value": "Sha Wellness Clinic", "displayName": "Sha Wellness Clinic"}, {"id": 151, "value": "Wlab", "displayName": "Wlab"}, {"id": 201, "value": "<PERSON><PERSON><PERSON> rofe", "displayName": "<PERSON><PERSON><PERSON> rofe"}, {"id": 73, "value": "Pullman Regional Hospital", "displayName": "Pullman Regional Hospital"}, {"id": 48, "value": "Shalvata Mental Health Center", "displayName": "Shalvata Mental Health Center"}, {"id": 10, "value": "Baruch Padeh Medical Center (Poriya Medical Center)", "displayName": "Baruch Padeh Medical Center (Poriya Medical Center)"}, {"id": 12, "value": "Beit Rivka Geriatric Medical Center", "displayName": "Beit Rivka Geriatric Medical Center"}, {"id": 14, "value": "Carmel Medical Center", "displayName": "Carmel Medical Center"}, {"id": 17, "value": "Dana-Dwek Children's Hospital (Ichilov)", "displayName": "Dana-Dwek Children's Hospital (Ichilov)"}, {"id": 30, "value": "Laniado Hospital – Sanz Medical Center", "displayName": "Laniado Hospital – Sanz Medical Center"}, {"id": 34, "value": "Maale Hacarmel Mental Health Medical Center", "displayName": "Maale Hacarmel Mental Health Medical Center"}, {"id": 50, "value": "<PERSON><PERSON><PERSON> Hospital", "displayName": "<PERSON><PERSON><PERSON> Hospital"}, {"id": 52, "value": "Soroka Medical Center", "displayName": "Soroka Medical Center"}, {"id": 74, "value": "95th Evacuation Hospital", "displayName": "95th Evacuation Hospital"}, {"id": 59, "value": "American Medical Laboratories (AML)", "displayName": "American Medical Laboratories (AML)"}, {"id": 54, "value": "Nahariya Western Galilee Medical Center", "displayName": "Nahariya Western Galilee Medical Center"}, {"id": 32, "value": "Lis Maternity & Women's Hospital (Ichilov)", "displayName": "Lis Maternity & Women's Hospital (Ichilov)"}, {"id": 130, "value": "Md Anderson Cancer Center", "displayName": "Md Anderson Cancer Center"}, {"id": 39, "value": "Natal- Israel Trauma and Resiliency Center", "displayName": "Natal- Israel Trauma and Resiliency Center"}, {"id": 78, "value": "Puget Sound Veterans Affairs Hospital ", "displayName": "Puget Sound Veterans Affairs Hospital "}, {"id": 171, "value": "Bascom Palmer Eye Institute", "displayName": "Bascom Palmer Eye Institute"}, {"id": 199, "value": "Spot Clinic", "displayName": "Spot Clinic"}, {"id": 204, "value": "Israel Defense Forces Medical Corps", "displayName": "Israel Defense Forces Medical Corps"}, {"id": 205, "value": "Porat Medical", "displayName": "Porat Medical"}, {"id": 206, "value": "Anigma Plus", "displayName": "Anigma Plus"}, {"id": 207, "value": "Northwell Health (Zucker)", "displayName": "Northwell Health (Zucker)"}, {"id": 208, "value": "Bernstein Medical", "displayName": "Bernstein Medical"}, {"id": 209, "value": "Atlanta Center for Restorative Dentistry", "displayName": ""}, {"id": 210, "value": "Herzelia Dermatology and Laser Center", "displayName": "Herzelia Dermatology and Laser Center"}, {"id": 211, "value": "<PERSON><PERSON> - podiatric medicine and laser surgery clinic", "displayName": "<PERSON><PERSON>"}, {"id": 212, "value": "CeGaT GmbH", "displayName": "CeGaT GmbH"}, {"id": 213, "value": "RogoMed", "displayName": "RogoMed"}, {"id": 214, "value": "Enzo Clinical Labs", "displayName": "Enzo Clinical Lab"}, {"id": 215, "value": " Pronto Diagnostics", "displayName": " Pronto Diagnostics"}], "lab_reports": [{"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "Actual base excess - arterial blood", "unit": "mmol/L", "id": 1}, {"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "Anion Gap - arterial blood", "unit": "mmol/L", "id": 3}, {"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "Base excess/deficit - arterial blood", "unit": "mosm/L", "id": 4}, {"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "Base excess/deficit - venous blood", "unit": "mosm/L", "id": 5}, {"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "Calcium Ionized - blood", "unit": "mmol/l", "id": 6}, {"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "Carboxyhemoglobin - blood", "unit": "%", "id": 7}, {"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "Chloride - arterial blood", "unit": "mmol/l", "id": 8}, {"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "Deoxyhemoglobin - blood", "unit": "%", "id": 9}, {"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "HCO3 (Bicarbonate) - arterial blood", "unit": "mmol/l", "id": 10}, {"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "HCO3 (Bicarbonate) - umbilical blood", "unit": "mmol/l", "id": 11}, {"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "HCO3 (Bicarbonate) - venous blood", "unit": "mmol/l", "id": 12}, {"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "Hematocrit - arterial blood", "unit": "%", "id": 13}, {"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "Hemoglobin total - arterial blood", "unit": "g/dl", "id": 14}, {"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "Hemoglobin total - umbilical blood", "unit": "g/dl", "id": 15}, {"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "Lactate (lactic acid) - arterial blood", "unit": "mmol/L", "id": 16}, {"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "Methemoglobin, quantitative - arterial blood", "unit": "%", "id": 17}, {"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "Oxyhemoglobin, quantitative - arterial blood", "unit": "%", "id": 18}, {"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "Oxyhemoglobin, quantitative - blood", "unit": "%", "id": 19}, {"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "pCO2 - arterial blood", "unit": "mmHG", "id": 20}, {"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "pCO2 - umbilical blood", "unit": "mmHG", "id": 21}, {"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "pCO2 - venous blood", "unit": "mmHG", "id": 22}, {"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "pH - arterial blood", "unit": "", "id": 23}, {"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "pH - umbilical blood", "unit": "", "id": 24}, {"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "pH - venous blood", "unit": "", "id": 25}, {"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "pO2 - arterial blood", "unit": "mmHG", "id": 26}, {"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "pO2 - umbilical blood", "unit": "mmHG", "id": 27}, {"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "pO2 - venous blood", "unit": "mmHG", "id": 28}, {"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "Potassium - arterial blood", "unit": "mmol/L", "id": 29}, {"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "Saturation O2 - arterial blood", "unit": "%", "id": 30}, {"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "Saturation O2 - venous blood", "unit": "%", "id": 31}, {"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "Sodium - arterial blood", "unit": "mmol/L", "id": 32}, {"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "tCO2 - arterial blood", "unit": "mmol/L", "id": 33}, {"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "tCO2 - umbilical blood", "unit": "mmol/L", "id": 34}, {"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "tCO2 - venous blood", "unit": "mmol/L", "id": 35}, {"sample_type": "Blood", "test_type": "Cancer Biomarkers", "parameter": "CA 15-3 tumor antigen (CA15-3) - blood", "unit": "U/ml", "id": 36}, {"sample_type": "Blood", "test_type": "Cancer Biomarkers", "parameter": "CA 19-9 tumor antigen (CA19-9) - blood", "unit": "U/ml", "id": 37}, {"sample_type": "Blood", "test_type": "Cancer Biomarkers", "parameter": "CA-125 tumor antigen - blood", "unit": "U/ml", "id": 38}, {"sample_type": "Blood", "test_type": "Cancer Biomarkers", "parameter": "Carcinoembryonic antigen (CEA) - blood", "unit": "ng/ml", "id": 39}, {"sample_type": "Blood", "test_type": "Cancer Biomarkers", "parameter": "Mucin-like Carcinoma-associated Antigen (MCA) - blood", "unit": "U/ml", "id": 40}, {"sample_type": "Blood", "test_type": "Cancer Biomarkers", "parameter": "Neuron-specific enolase (NSE) tumor antigen", "unit": "ng/mL", "id": 41}, {"sample_type": "Blood", "test_type": "Cancer Biomarkers", "parameter": "Neuron-specific enolase (NSE) tumor antigen (COBAS)", "unit": "ng/mL", "id": 42}, {"sample_type": "Blood", "test_type": "Cancer Biomarkers", "parameter": "Prostate specific antigen free (PSA free) - blood", "unit": "ng/ml", "id": 43}, {"sample_type": "Blood", "test_type": "Cancer Biomarkers", "parameter": "Prostate specific antigen free/total ratio (PSA free/total) - blood", "unit": "%", "id": 44}, {"sample_type": "Blood", "test_type": "Cancer Biomarkers", "parameter": "Prostate specific antigen total (PSA total) - blood", "unit": "ng/ml", "id": 45}, {"sample_type": "Blood", "test_type": "Cancer Biomarkers", "parameter": "Tumor Antigen other antigen - blood", "unit": "U/ml", "id": 46}, {"sample_type": "Blood", "test_type": "Cardiac Biomarkers", "parameter": "Brain natriuretic peptide (BNP) - blood", "unit": "pg/ml", "id": 47}, {"sample_type": "Blood", "test_type": "Cardiac Biomarkers", "parameter": "Coronary Risk Ratio", "unit": "", "id": 48}, {"sample_type": "Blood", "test_type": "Cardiac Biomarkers", "parameter": "Pro-BNP", "unit": "pg/ml", "id": 49}, {"sample_type": "Blood", "test_type": "Cardiac Biomarkers", "parameter": "Troponin I - blood", "unit": "ng/L", "id": 50}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "ADAMTS-13 Ab - blood", "unit": "U/ml", "id": 51}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "ADAMTS-13 activity - blood", "unit": "IU/ml", "id": 52}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Clotting factor II - blood", "unit": "%", "id": 53}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Clotting factor IX - blood", "unit": "%", "id": 54}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Clotting factor V (AcG) - blood", "unit": "%", "id": 55}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Clotting factor VII - blood", "unit": "%", "id": 56}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Clotting factor VIII - blood", "unit": "%", "id": 57}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Clotting factor X - blood", "unit": "%", "id": 58}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Clotting factor XI - blood", "unit": "%", "id": 59}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Clotting factor XII - blood", "unit": "%", "id": 60}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Clotting factor XIII (fibrin stabilizing) - blood", "unit": "%", "id": 61}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Danatest", "unit": "", "id": 62}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Di-Dimer FEU (D-Dimer)", "unit": "FEU mg/L", "id": 63}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Fibrinogen (Fgn) (g/L)", "unit": "g/L", "id": 64}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Fibrinogen (Fgn) (mg/dl)", "unit": "mg/dL", "id": 65}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "FS actin PTT - blood", "unit": "Sec", "id": 66}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Kaolin clotting time (KCT), index - blood", "unit": "", "id": 67}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Kaolin clotting time (KCT), mix - blood", "unit": "Sec", "id": 68}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Kaolin clotting time (KCT), N - blood", "unit": "Sec", "id": 69}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Kaolin clotting time (KCT), PT - blood", "unit": "Sec", "id": 70}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Osmotic fragility incubated At 0% NaCl 24h - blood", "unit": "NaCl", "id": 71}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Osmotic fragility incubated At 0.10% NaCl 24h - blood", "unit": "NaCl", "id": 72}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Plasminogen - blood", "unit": "%", "id": 73}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Platelet aggregation ADP", "unit": "%", "id": 74}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Platelet aggregation ADP N1", "unit": "%", "id": 75}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Platelet aggregation arach", "unit": "%", "id": 76}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Platelet aggregation arach. Collagen", "unit": "%", "id": 77}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Platelet aggregation arach. N1", "unit": "%", "id": 78}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Platelet aggregation collagen N1 - blood", "unit": "%", "id": 79}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Platelet aggregation epinephrine", "unit": "%", "id": 80}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Platelet aggregation epinephrine N1 - blood", "unit": "%", "id": 81}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Platelet aggregation Ristocetin", "unit": "%", "id": 82}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Platelet aggregation Ristocetin N1 - blood", "unit": "%", "id": 83}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Platelet aggregation Ristocetin N3 - blood", "unit": "%", "id": 84}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Platelet aggregation Ristocetin PT2 - blood", "unit": "%", "id": 85}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Protein C, activity", "unit": "%", "id": 86}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Protein S Ag total - blood", "unit": "%", "id": 87}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Protein S free Ag - blood", "unit": "%", "id": 88}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "PT INR (INR)", "unit": "", "id": 89}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "PT mixure 50% normal - blood", "unit": "Sec", "id": 90}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "PT sec (PT)", "unit": "Sec", "id": 91}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "PTT mixure 50% normal - blood", "unit": "Sec", "id": 92}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "PTT sec (PTT)", "unit": "Sec", "id": 93}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Reptilase time panel - blood", "unit": "Sec", "id": 94}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Ristocetine cofactor", "unit": "%", "id": 95}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Russell VV confirm mix - blood", "unit": "Sec", "id": 96}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Russell VV screen mix - blood", "unit": "Sec", "id": 97}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Russell VV screen mix ratio - blood", "unit": "", "id": 98}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "RVV (Russell viper venom) confirm N - blood", "unit": "", "id": 99}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "RVV (Russell viper venom) time - blood", "unit": "Sec", "id": 100}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "RVVT ratio", "unit": "", "id": 101}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "SCT, 1:1 mixture with normal plasma, ratio - blood", "unit": "", "id": 102}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "SCT, LAC sensitive/insensitive reagent ratio - blood", "unit": "", "id": 103}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "TEG (Thromboelastogram) coagulation index - blood", "unit": "", "id": 104}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "TEG angle", "unit": "deg", "id": 105}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "TEG kinetics", "unit": "Min", "id": 106}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "TEG LY30", "unit": "%", "id": 107}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "TEG maximun amplitude", "unit": "mmol/L", "id": 108}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "TEG R-time", "unit": "Min", "id": 109}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "Thrmbin time (TT)", "unit": "Sec", "id": 110}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "VWF (<PERSON> factor) activity - blood", "unit": "%", "id": 111}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "VWF (<PERSON> factor) Ag", "unit": "%", "id": 112}, {"sample_type": "Blood", "test_type": "Complete Blood Count (CBC)", "parameter": "Basophils % (BASO%)", "unit": "%", "id": 113}, {"sample_type": "Blood", "test_type": "Complete Blood Count (CBC)", "parameter": "Basophils No. (BASO#)", "unit": "10e3/microL", "id": 114}, {"sample_type": "Blood", "test_type": "Complete Blood Count (CBC)", "parameter": "Eosinophils % (EOS%)", "unit": "%", "id": 115}, {"sample_type": "Blood", "test_type": "Complete Blood Count (CBC)", "parameter": "Eosinophils No. (EOS#)", "unit": "10e3/microL", "id": 116}, {"sample_type": "Blood", "test_type": "Complete Blood Count (CBC)", "parameter": "<PERSON><PERSON><PERSON><PERSON> (HCT)", "unit": "%", "id": 117}, {"sample_type": "Blood", "test_type": "Complete Blood Count (CBC)", "parameter": "Hemoglobin (Hb)", "unit": "g/dL", "id": 118}, {"sample_type": "Blood", "test_type": "Complete Blood Count (CBC)", "parameter": "Lymphocytes % (LYM%)", "unit": "%", "id": 119}, {"sample_type": "Blood", "test_type": "Complete Blood Count (CBC)", "parameter": "Lymphocytes No. (LYM#)", "unit": "10e3/microL", "id": 120}, {"sample_type": "Blood", "test_type": "Complete Blood Count (CBC)", "parameter": "MCH", "unit": "pg", "id": 121}, {"sample_type": "Blood", "test_type": "Complete Blood Count (CBC)", "parameter": "MCHC", "unit": "g/dL", "id": 122}, {"sample_type": "Blood", "test_type": "Complete Blood Count (CBC)", "parameter": "MCV", "unit": "fL", "id": 123}, {"sample_type": "Blood", "test_type": "Complete Blood Count (CBC)", "parameter": "Mean Platelet Volume (MPV)", "unit": "fl", "id": 124}, {"sample_type": "Blood", "test_type": "Complete Blood Count (CBC)", "parameter": "Monocytes % (MONO%)", "unit": "%", "id": 125}, {"sample_type": "Blood", "test_type": "Complete Blood Count (CBC)", "parameter": "Monocytes No. (MONO#)", "unit": "10e3/microL", "id": 126}, {"sample_type": "Blood", "test_type": "Complete Blood Count (CBC)", "parameter": "Neutrophil/lymphocte ratio (NLR)", "unit": "", "id": 127}, {"sample_type": "Blood", "test_type": "Complete Blood Count (CBC)", "parameter": "Neutrophils % (NEUT%)", "unit": "%", "id": 128}, {"sample_type": "Blood", "test_type": "Complete Blood Count (CBC)", "parameter": "Neutrophils No. (NEUT#)", "unit": "10e3/microL", "id": 129}, {"sample_type": "Blood", "test_type": "Complete Blood Count (CBC)", "parameter": "Nucleated RBC (NRBC)", "unit": "/100WBC", "id": 130}, {"sample_type": "Blood", "test_type": "Complete Blood Count (CBC)", "parameter": "Platelet count (PLT)", "unit": "10e3/microL", "id": 131}, {"sample_type": "Blood", "test_type": "Complete Blood Count (CBC)", "parameter": "RBC", "unit": "10e6/microL", "id": 132}, {"sample_type": "Blood", "test_type": "Complete Blood Count (CBC)", "parameter": "RDW", "unit": "%", "id": 133}, {"sample_type": "Blood", "test_type": "Complete Blood Count (CBC)", "parameter": "Total iron binding capacity", "unit": "microg/dL", "id": 134}, {"sample_type": "Blood", "test_type": "Complete Blood Count (CBC)", "parameter": "Unsaturated iron-binding capacity (UIBC)", "unit": "microg/dL", "id": 135}, {"sample_type": "Blood", "test_type": "Complete Blood Count (CBC)", "parameter": "WBC", "unit": "10e3/microL", "id": 136}, {"sample_type": "Cerebrospinal fluid (CSF)", "test_type": "CSF analysis", "parameter": "Protein total - CSF", "unit": "mg/dL", "id": 137}, {"sample_type": "Blood", "test_type": "Drug Panel", "parameter": "Carbamezapine", "unit": "microg/ml", "id": 138}, {"sample_type": "Blood", "test_type": "Drug Panel", "parameter": "<PERSON><PERSON><PERSON><PERSON>, quantitative - blood", "unit": "ng/ml", "id": 139}, {"sample_type": "Blood", "test_type": "Drug Panel", "parameter": "Cyclosporine - blood", "unit": "ng/ml", "id": 140}, {"sample_type": "Blood", "test_type": "Drug Panel", "parameter": "Digoxin - blood", "unit": "ng/ml", "id": 141}, {"sample_type": "Blood", "test_type": "Drug Panel", "parameter": "Ethanol - blood", "unit": "mg/dL", "id": 142}, {"sample_type": "Blood", "test_type": "Drug Panel", "parameter": "<PERSON><PERSON><PERSON> (Certican), quantitative", "unit": "ng/ml", "id": 143}, {"sample_type": "Blood", "test_type": "Drug Panel", "parameter": "Gentamicin - blood", "unit": "microg/ml", "id": 144}, {"sample_type": "Blood", "test_type": "Drug Panel", "parameter": "Lamotrigine - blood", "unit": "mg/L", "id": 145}, {"sample_type": "Blood", "test_type": "Drug Panel", "parameter": "Levetiracetam (Keppra) quantitative - blood", "unit": "?g/mL", "id": 146}, {"sample_type": "Blood", "test_type": "Drug Panel", "parameter": "Lithium - blood", "unit": "mmol/L", "id": 147}, {"sample_type": "Blood", "test_type": "Drug Panel", "parameter": "<PERSON><PERSON><PERSON><PERSON><PERSON>, quantitative - blood", "unit": "ng/ml", "id": 148}, {"sample_type": "Blood", "test_type": "Drug Panel", "parameter": "Phenobarbital (luminal) - blood", "unit": "microg/ml", "id": 149}, {"sample_type": "Blood", "test_type": "Drug Panel", "parameter": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Idantoin) - blood", "unit": "microg/ml", "id": 150}, {"sample_type": "Blood", "test_type": "Drug Panel", "parameter": "Quinidine - blood", "unit": "microg/ml", "id": 151}, {"sample_type": "Blood", "test_type": "Drug Panel", "parameter": "<PERSON><PERSON><PERSON> (Rapamune)", "unit": "ng/ml", "id": 152}, {"sample_type": "Blood", "test_type": "Drug Panel", "parameter": "<PERSON><PERSON><PERSON><PERSON> (FK-506) - blood", "unit": "ng/ml", "id": 153}, {"sample_type": "Blood", "test_type": "Drug Panel", "parameter": "Theophiline - blood", "unit": "microg/ml", "id": 154}, {"sample_type": "Blood", "test_type": "Drug Panel", "parameter": "Topiramate - blood", "unit": "microg/ml", "id": 155}, {"sample_type": "Blood", "test_type": "Drug Panel", "parameter": "Tricyclic antidepressants - blood", "unit": "ng/ml", "id": 156}, {"sample_type": "Blood", "test_type": "Drug Panel", "parameter": "Valproic Acid (dapkene) - blood", "unit": "microg/ml", "id": 157}, {"sample_type": "Blood", "test_type": "Drug Panel", "parameter": "Vancomycin - blood", "unit": "microg/ml", "id": 158}, {"sample_type": "Blood", "test_type": "Drug Panel", "parameter": "Voriconazole, quantitative - blood", "unit": "microg/ml", "id": 159}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "17-OH-Progesterone", "unit": "ng/ml", "id": 160}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Adrenocorticotropic Hormone (ACTH)", "unit": "pg/ml", "id": 161}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Aldosterone (at rest) - blood", "unit": "pg/ml", "id": 162}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Aldosterone (at stress) - blood", "unit": "pg/ml", "id": 163}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Androstenedione (D4)", "unit": "ng/ml", "id": 164}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Beta CrossLaps (CTX) (Ichilov)", "unit": "pg/ml", "id": 165}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Beta CrossLaps (CTX) (COBAS)", "unit": "pg/ml", "id": 166}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Beta Human Chorionic Gonadotropine (hCG) Quantitative", "unit": "microIU/ml", "id": 167}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Beta Human Chorionic Gonadotropine (hCG) Quantitative", "unit": "microIU/ml", "id": 168}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "CA-72.4", "unit": "U/ml", "id": 169}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Calcitonin", "unit": "pg/mL", "id": 170}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Chromogranin A", "unit": "ng/ml", "id": 171}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Compound-S", "unit": "microg%", "id": 172}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Cortisol (evening) - blood", "unit": "microg/100ml", "id": 173}, {"sample_type": "Sliva", "test_type": "Endocrinology", "parameter": "Cortisol (evening) - salivary", "unit": "microg/100ml", "id": 174}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Cortisol (morning) - blood", "unit": "microg/100ml", "id": 175}, {"sample_type": "Sliva", "test_type": "Endocrinology", "parameter": "Cortisol (morning) - salivary", "unit": "microg/100ml", "id": 176}, {"sample_type": "<PERSON><PERSON>", "test_type": "Endocrinology", "parameter": "Cortisol free - urine", "unit": "microg/24h", "id": 177}, {"sample_type": "<PERSON><PERSON>", "test_type": "Endocrinology", "parameter": "Cortisol free (HPLC) - urine", "unit": "microg/24h", "id": 178}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "C-Peptide", "unit": "ng/ml", "id": 179}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Deoxypyridinoline", "unit": "nmol/mmol creatinine", "id": 180}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Dexamethasone HPLC", "unit": "", "id": 181}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "DHEA-S (Dehydroepiandrosterone)", "unit": "microg/ml", "id": 182}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Erythropoietin", "unit": "MIU/ml", "id": 183}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Estradiol 17-beta (E2) - blood", "unit": "pg/ml", "id": 184}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Follicle Stimulating Hormone (FSH)", "unit": "microU/ml", "id": 185}, {"sample_type": "<PERSON><PERSON>", "test_type": "Endocrinology", "parameter": "FRAC. 17 KETO: Androsterone - urine", "unit": "mg/24h", "id": 186}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "FRAC. 17 KETO: Dehydroepiandrosterone", "unit": "mg/24h", "id": 187}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "FRAC. 17 KETO: Etiocholanolone", "unit": "mg/24h", "id": 188}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "GAD Ab (glutamic acid decarboxylase)", "unit": "U/ml", "id": 189}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "<PERSON><PERSON><PERSON>", "unit": "pg/ml", "id": 190}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Growth Hormone (GH)", "unit": "ng/ml", "id": 191}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Homocystien", "unit": "nmol/L", "id": 192}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "IGF-1 (Insulin Like Growth Factor 1)", "unit": "ng/ml", "id": 193}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "IGF-1 Tanner Stage 1", "unit": "", "id": 194}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "IGF-1 Tanner Stage 2", "unit": "", "id": 195}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "IGF-1 Tanner Stage 3", "unit": "", "id": 196}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "IGF-1 Tanner Stage 4", "unit": "", "id": 197}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "IGF-1 Tanner Stage 5", "unit": "", "id": 198}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "<PERSON><PERSON><PERSON>", "unit": "microU/ml", "id": 199}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "<PERSON><PERSON><PERSON>", "unit": "U/ml", "id": 200}, {"sample_type": "<PERSON><PERSON>", "test_type": "Endocrinology", "parameter": "Iodine - urine", "unit": "microg/l", "id": 201}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Luteinizing Hormone (LH)", "unit": "microU/ml", "id": 202}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Metanephrine - blood", "unit": "pg/ml", "id": 203}, {"sample_type": "<PERSON><PERSON>", "test_type": "Endocrinology", "parameter": "Metanephrine - urine", "unit": "mg/24h", "id": 204}, {"sample_type": "<PERSON><PERSON>", "test_type": "Endocrinology", "parameter": "Methoxytyramine 3 - urine", "unit": "mg/24h", "id": 205}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "MIF (Anti-Mullerian Hormone - AMH)", "unit": "ng/ml", "id": 206}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Normetanephrine - blood", "unit": "pg/ml", "id": 207}, {"sample_type": "<PERSON><PERSON>", "test_type": "Endocrinology", "parameter": "Normetanephrine - urine", "unit": "", "id": 208}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Parathyroid hormone (PTH) intact - blood", "unit": "pg/ml", "id": 209}, {"sample_type": "<PERSON><PERSON>", "test_type": "Endocrinology", "parameter": "Pregnandiol (P2) - urine", "unit": "mg/24h", "id": 210}, {"sample_type": "<PERSON><PERSON>", "test_type": "Endocrinology", "parameter": "Pregantriol (P3) - urine", "unit": "mg/24h", "id": 211}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Procollagen I aminoterminal peptide total (P1NP) - blood", "unit": "ng/ml", "id": 212}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Progesterone", "unit": "ng/ml", "id": 213}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Progesterone (P4) - blood", "unit": "ng/ml", "id": 214}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Prolactine (PRL) - blood", "unit": "ng/ml", "id": 215}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Renin Activity (at rest)", "unit": "ng/ml/h", "id": 216}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Renin Activity (at stress)", "unit": "ng/ml/h", "id": 217}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Testosterone Bio-available", "unit": "ng/ml", "id": 218}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Testosterone Bio-available %", "unit": "%", "id": 219}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Testosterone total (TTST) - blood", "unit": "ng/ml", "id": 220}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Thryoxine (T4), free - blood", "unit": "ng/dL", "id": 221}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Thyroglobulin", "unit": "ng/ml", "id": 222}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Thyroid stimulating hormone (TSH) - blood", "unit": "mu/L", "id": 223}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Thyroid Stimulating Immunoglobulins (TSI)", "unit": "%", "id": 224}, {"sample_type": "<PERSON><PERSON>", "test_type": "Endocrinology", "parameter": "Total 17-Keto Steroids - urine", "unit": "mg/24h", "id": 225}, {"sample_type": "<PERSON><PERSON>", "test_type": "Endocrinology", "parameter": "Total 17-OH Corticosteroids - urine", "unit": "mg/24h", "id": 226}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Triiodothyronin (T3) total - blood", "unit": "ng/dL", "id": 227}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Triiodothyronin free (fT3) - blood", "unit": "pmol/L", "id": 228}, {"sample_type": "Blood", "test_type": "Hematology", "parameter": "Anticoagulants", "unit": "", "id": 229}, {"sample_type": "Blood", "test_type": "Hematology", "parameter": "Antithrombin III", "unit": "%", "id": 230}, {"sample_type": "Blood", "test_type": "Hematology", "parameter": "Antithrombin III activity - blood", "unit": "%", "id": 231}, {"sample_type": "Blood", "test_type": "Hematology", "parameter": "Leukocyte Alkaline Phosphatase (LAP)", "unit": "score/100", "id": 232}, {"sample_type": "Cerebrospinal fluid (CSF)", "test_type": "Hematology", "parameter": "Leukocytes - CSF", "unit": "cells/microl", "id": 233}, {"sample_type": "Blood", "test_type": "Hematology", "parameter": "LUC%", "unit": "%", "id": 234}, {"sample_type": "Cerebrospinal fluid (CSF)", "test_type": "Hematology", "parameter": "Monocytes - CSF", "unit": "cells", "id": 235}, {"sample_type": "Blood", "test_type": "Hematology", "parameter": "Normoblasts - blood", "unit": "", "id": 236}, {"sample_type": "Cerebrospinal fluid (CSF)", "test_type": "Hematology", "parameter": "Red Blood Cells (RBC) - CSF", "unit": "cells/microl", "id": 237}, {"sample_type": "Blood", "test_type": "Hematology", "parameter": "Reticulocytes %", "unit": "%", "id": 238}, {"sample_type": "Blood", "test_type": "Hematology", "parameter": "Reticulocytes No.", "unit": "10e6/microl", "id": 239}, {"sample_type": "Cerebrospinal fluid (CSF)", "test_type": "Immunology", "parameter": "Albumin - CSF", "unit": "mg/l", "id": 240}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Albumin electrophoresis (EP) - blood", "unit": "gr/l", "id": 241}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Albumin electrophoresis (EP) % - blood", "unit": "%", "id": 242}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Albumin/Globulin <PERSON> (A/G) - blood", "unit": "", "id": 243}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Alkaline phosphatase isoenzyme, bone - blood", "unit": "U/l", "id": 244}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Alkaline phosphatase isoenzyme, fast liver - blood", "unit": "U/l", "id": 245}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Alkaline phosphatase isoenzyme, intestine - blood", "unit": "U/l", "id": 246}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Alkaline phosphatase isoenzyme, liver 1 - blood", "unit": "U/l", "id": 247}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Alpha 1-Globulin (EP) - blood", "unit": "gr/l", "id": 248}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Alpha 1-Globulin (EP) % - blood", "unit": "%", "id": 249}, {"sample_type": "<PERSON><PERSON>", "test_type": "Immunology", "parameter": "Alpha 2-Globulin (EP) - urine", "unit": "gr/l", "id": 250}, {"sample_type": "<PERSON><PERSON>", "test_type": "Immunology", "parameter": "Alpha 2-Globulin (EP) % - urine", "unit": "%", "id": 251}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Anti-double strand DNA (dsDNA) Ab IgG - blood", "unit": "U/ml", "id": 252}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Anti-ENA (extractable nuclear Ag) Ab - blood", "unit": "Index", "id": 253}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Anti-ENA (extractable nuclear Ag) Ab - blood", "unit": "", "id": 254}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Anti-mitochondrial M2 Ab", "unit": "UI/ml", "id": 255}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Anti-single strand DNA (ssDNA) Ab - blood", "unit": "U/ml", "id": 256}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Aquaporin 4 Ab, Elisa - blood", "unit": "U/ml", "id": 257}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "B cells %", "unit": "%", "id": 258}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Beta-2 Microglobulin - blood", "unit": "mg/l", "id": 259}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Beta-Globulin (EP) - blood", "unit": "gr/l", "id": 260}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Beta-Globulin (EP) % - blood", "unit": "%", "id": 261}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "C1 esterase inhibitor - blood", "unit": "mg/dl", "id": 262}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Cardiolipin IgG Ab - blood", "unit": "U/ml", "id": 263}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Cardiolipin IgM Ab - blood", "unit": "U/ml", "id": 264}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "CD16-CD56 total per cmm, FACS", "unit": "", "id": 265}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "CD19 FACS - blood", "unit": "", "id": 266}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "CD2-CD56 NK (natural killer), FACS - blood", "unit": "", "id": 267}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "CD3 % (mature T-cells)", "unit": "%", "id": 268}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "CD3 total per cmm, FACS - blood", "unit": "", "id": 269}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "CD4 %", "unit": "%", "id": 270}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "CD4 total per cmm, FACS - blood", "unit": "", "id": 271}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "CD4/CD8 cells ratio, FACS - blood", "unit": "", "id": 272}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "CD56 FACS - blood", "unit": "", "id": 273}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "CD8 %", "unit": "%", "id": 274}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "CD8 total per cmm, FACS - blood", "unit": "", "id": 275}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Centromere Ab - blood", "unit": "U/ml", "id": 276}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Complement C3 - blood", "unit": "g/l", "id": 277}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Complement C4 - blood", "unit": "g/l", "id": 278}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Complement total hemolytic activity (CH50) - blood", "unit": "U/ml", "id": 279}, {"sample_type": "Nasal swab", "test_type": "Immunology", "parameter": "COVID-19 (PCR)", "unit": "-", "id": 280}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "COVID-19 IgG", "unit": "U/mL", "id": 281}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Gamma - Globulin (EP) - blood", "unit": "gr/l", "id": 282}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Gamma - Globulin (EP) % - blood", "unit": "%", "id": 283}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Gliadin Peptide Ab IgA - blood", "unit": "U/ml", "id": 284}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Glomerular Basement Membrane (GBM) Ab - blood", "unit": "U/ml", "id": 285}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Haptoglobin - blood", "unit": "mg/l", "id": 286}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Helicobacter Pylori Antibody lg - blood", "unit": "mmol/l", "id": 287}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Hepatitis A Ab (HAAb) IgG - blood", "unit": "", "id": 288}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Hepatitis A Ab (HAAb) IgM - blood", "unit": "", "id": 289}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Hepatitis B Core Ab (HBcAb) IgM - blood", "unit": "", "id": 290}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Hepatitis B Core Ab (HBcAb) total - blood", "unit": "S/CO", "id": 291}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Hepatitis B E Ab (HBeAb) - blood", "unit": "S/CO", "id": 292}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Hepatitis B E Antigen (HBeAg) - blood", "unit": "S/N", "id": 293}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Hepatitis B Sureface Ab (HBsAb) - blood", "unit": "mUI/ml", "id": 294}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Hepatitis B Sureface Ag (HBsAg) - blood", "unit": "", "id": 295}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Hepatitis C Ab (HCAb) - blood", "unit": "", "id": 296}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "High Sensitivity CRP - blood", "unit": "mg/l", "id": 297}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Histones Ab - blood", "unit": "U/ml", "id": 298}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "IgA Ab - blood", "unit": "g/l", "id": 299}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "IgA sensitive/pediatric - blood", "unit": "g/l", "id": 300}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "IgD Ab", "unit": "mg/L", "id": 301}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "IgG Ab - blood", "unit": "g/L", "id": 302}, {"sample_type": "Cerebrospinal fluid (CSF)", "test_type": "Immunology", "parameter": "IgG Ab - CSF", "unit": "mg/dL", "id": 303}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "IgG Index", "unit": "", "id": 304}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "IgG1", "unit": "g/L", "id": 305}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "IgG2", "unit": "g/L", "id": 306}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "IgG3", "unit": "g/L", "id": 307}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "IgG4", "unit": "g/L", "id": 308}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "IgM Ab - blood", "unit": "g/L", "id": 309}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Intrinsic Factor (IF) Ab - blood", "unit": "U/ml", "id": 310}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Islet Cell Ab - blood", "unit": "<PERSON><PERSON>", "id": 311}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Lactate Dehydrogenase 1 (LD1) - blood", "unit": "U/L", "id": 312}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Lactate Dehydrogenase 2 (LD2) - blood", "unit": "U/L", "id": 313}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Lactate Dehydrogenase 3 (LD3) - blood", "unit": "U/L", "id": 314}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Lactate Dehydrogenase 4 (LD4) - blood", "unit": "U/L", "id": 315}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Lactate Dehydrogenase 5 (LD5) - blood", "unit": "U/L", "id": 316}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Lymphocytes, total", "unit": "", "id": 317}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Myeloperoxidase (MPO) Ab - blood", "unit": "U/ml", "id": 318}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Natural Killer (NK) %", "unit": "%", "id": 319}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Proteinase 3 (PR3) Ab - blood", "unit": "U/ml", "id": 320}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Rheumatoid Factor (RF) quantitative - blood", "unit": "IU/ml", "id": 321}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Saccharomyces cerevisiae IgA Ab - blood", "unit": "U/ml", "id": 322}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Saccharomyces cerevisiae IgG Ab - blood", "unit": "U/ml", "id": 323}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Tissue Transglutaminase (tTG) Ab IGA", "unit": "U/ml", "id": 324}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Transglutaminase IgG - blood", "unit": "U/ml", "id": 325}, {"sample_type": "Blood", "test_type": "Lipid Profile", "parameter": "Cholesterol total (Chol) - blood", "unit": "mg/dL", "id": 326}, {"sample_type": "Blood", "test_type": "Lipid Profile", "parameter": "HDL cholesterol (HDL) - blood", "unit": "mg/dL", "id": 327}, {"sample_type": "Blood", "test_type": "Lipid Profile", "parameter": "LDL cholesterol (LDL) - blood", "unit": "mg/dL", "id": 328}, {"sample_type": "Blood", "test_type": "Lipid Profile", "parameter": "Triglycerides (TG) - blood", "unit": "mg/dL", "id": 329}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "6-M<PERSON> (6-Methylmercaptopurine), HPLC quantitative - blood", "unit": "pmole/8x10^8 RBC", "id": 330}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "6-TGN (6-thioguanine-nucleotide), HPLC quantitative - blood", "unit": "pmole/8x10^8 RBC", "id": 331}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "ACE (Angiotensin Converting Enzyme)", "unit": "U/L", "id": 332}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Acetaminophene (Acamol)", "unit": "microg/ml", "id": 333}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Acetylcarnitine - blood", "unit": "micromol/L", "id": 334}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Adenosylhomocysteine, quantitative - blood", "unit": "micromol/L", "id": 335}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Alanine - blood", "unit": "micromol/L", "id": 336}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Alanine amino transferase, ALT (GPT) - blood", "unit": "U/L", "id": 337}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Albumin (Alb) - blood", "unit": "gr/L", "id": 338}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Alkaline Phosphatase (Alk Phos) - blood", "unit": "U/L", "id": 339}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Alloisoleucine - blood", "unit": "micromol/L", "id": 340}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Alpha amino adipic acid - blood", "unit": "micromol/L", "id": 341}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Alpha fetoprotein (AFP) - blood", "unit": "ng/mL", "id": 342}, {"sample_type": "Cerebrospinal fluid (CSF)", "test_type": "Metabolic Panel", "parameter": "Alpha fetoprotein (AFP) - CSF", "unit": "ng/mL", "id": 343}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Ammonia - blood", "unit": "microg/dL", "id": 344}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Amylase - blood", "unit": "U/L", "id": 345}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Apolipprotien A1", "unit": "mg/dL", "id": 346}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Apolipprotien B100", "unit": "mg/dL", "id": 347}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Arginine", "unit": "micromol/L", "id": 348}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Argininosuccinic acid - blood", "unit": "micromol/L", "id": 349}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Aspargine - blood", "unit": "micromol/L", "id": 350}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Aspartic acid - blood", "unit": "micromol/L", "id": 351}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Aspratate aminotransferase, AST (GOT) - blood", "unit": "U/L", "id": 352}, {"sample_type": "<PERSON><PERSON>", "test_type": "Metabolic Panel", "parameter": "Benzodiazepines - urine", "unit": "ng/ml", "id": 353}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Beta Alanine - blood", "unit": "micromol/L", "id": 354}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Beta amino isobutyric acid - blood", "unit": "micromol/L", "id": 355}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Bile Acids - blood", "unit": "microM/L", "id": 356}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Bilirubin direct (D Bil) - blood", "unit": "mg/dL", "id": 357}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Bilirubin indirect (ID Bil) - blood", "unit": "mg/dL", "id": 358}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Bilirubin neonatal - blood", "unit": "mg/dL", "id": 359}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Bilirubin total (T Bil) - blood", "unit": "mg/dL", "id": 360}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Butyrylcarnitine - blood", "unit": "micromol/L", "id": 361}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Calcium total (Ca) - blood", "unit": "mg/dL", "id": 362}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Carnitine, free - blood", "unit": "micromol/L", "id": 363}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Carnitine, total - blood", "unit": "micromol/L", "id": 364}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Carnosine - blood", "unit": "micromol/L", "id": 365}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Ceruloplasmin - blood", "unit": "mg/dL", "id": 366}, {"sample_type": "Sweat", "test_type": "Metabolic Panel", "parameter": "Chloride - sweat", "unit": "mmol/l", "id": 367}, {"sample_type": "<PERSON><PERSON>", "test_type": "Metabolic Panel", "parameter": "Chloride - urine", "unit": "mmol/l", "id": 368}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Chloride (Cl) - blood", "unit": "mmol/l", "id": 369}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Cholinesterase pseudo - blood", "unit": "IU/l", "id": 370}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Citrulline - blood", "unit": "micromol/L", "id": 371}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Coenzyme Q10 - blood", "unit": "mg/L", "id": 372}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Copper - blood", "unit": "microg/dl", "id": 373}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "<PERSON><PERSON><PERSON> (CK, CPK) - blood", "unit": "U/L", "id": 374}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Creatinine (Cr) - blood", "unit": "mg/dL", "id": 375}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Creatinine kinase MB activity (CKMB)", "unit": "U/L", "id": 376}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Creatinine kinase MB activity (CKMB) %", "unit": "%", "id": 377}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "CRP Wide Range (WCRP)", "unit": "mg/L", "id": 378}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Cystathionine - blood", "unit": "micromol/L", "id": 379}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Cysteine sulfate, quantitative - blood", "unit": "micromol/L", "id": 380}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Cystine - blood", "unit": "micromol/L", "id": 381}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Decenoylcarnitine - blood", "unit": "micromol/L", "id": 382}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Dodecanoylcarnitine - blood", "unit": "micromol/L", "id": 383}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "DOPA - blood", "unit": "pg/ml", "id": 384}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "eGFR", "unit": "ml/min/1.73m^2", "id": 385}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Epinephrine - blood", "unit": "pg/ml", "id": 386}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Esterified/ Free Carnitine Ratio", "unit": "", "id": 387}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Ethanolamine - blood", "unit": "micromol/L", "id": 388}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Fe<PERSON>tin (Fer) - blood", "unit": "ng/ml", "id": 389}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Folic acid - blood (FolA)", "unit": "ng/ml", "id": 390}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "G6PD/ hemoglobin", "unit": "U/gr Hb", "id": 391}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "GABA (Gamma amino butyric acid) - blood", "unit": "micromol/L", "id": 392}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Gamma glutamyltransferase (GGT) - blood", "unit": "U/L", "id": 393}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Globulin (Glob) - blood", "unit": "gr/L", "id": 394}, {"sample_type": "Amniotic Fluid", "test_type": "Metabolic Panel", "parameter": "Glucose - amniotic fluid (quantitative)", "unit": "mg/dL", "id": 395}, {"sample_type": "Cerebrospinal fluid (CSF)", "test_type": "Metabolic Panel", "parameter": "Glucose - CSF (quantitative)", "unit": "mg/dL", "id": 396}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Glucose (Glu) - blood", "unit": "mg/dL", "id": 397}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Glucose-6-phosphate dehydrogenase (G6PD) - RBC", "unit": "U/dl", "id": 398}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Glutamic acid - blood", "unit": "micromol/L", "id": 399}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Glutamine - blood", "unit": "micromol/L", "id": 400}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Glycine - blood", "unit": "micromol/L", "id": 401}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Hemoglobin A1C (HbA1C) - hemolyzate", "unit": "gr/dl", "id": 402}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Hemoglobin A1C# (HbA1C#)", "unit": "mmol/mol", "id": 403}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Hemoglobin A1C% (HbA1C%)", "unit": "%", "id": 404}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Hemoglobin A2 (EP) - blood", "unit": "%", "id": 405}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Hemoglobin C (EP) - blood", "unit": "%", "id": 406}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Hemoglobin E (EP) - blood", "unit": "%", "id": 407}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Hemoglobin F, Alkali denaturation - blood", "unit": "%", "id": 408}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Hemoglobin H (EP) - blood", "unit": "%", "id": 409}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Hemoglobin S (EP) - blood", "unit": "%", "id": 410}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Hemog<PERSON><PERSON> (EP) - blood", "unit": "%", "id": 411}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Hexadecanoylcarnitine, quantitive - blood", "unit": "?mol/L", "id": 412}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Hexanoylcarnitine - blood", "unit": "?mol/L", "id": 413}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Histidine - blood", "unit": "?mol/L", "id": 414}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Homocystien - blood", "unit": "?mol/L", "id": 415}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Hydroxylysine, quantitative - blood", "unit": "?mol/L", "id": 416}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Hydroxyproline, quantitative - blood", "unit": "?mol/L", "id": 417}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Iron (Fe) - blood", "unit": "microg/dL", "id": 418}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Isoleucine - blood", "unit": "micromol/l", "id": 419}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Isovalerylcarnitine - blood", "unit": "micromol/l", "id": 420}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Lactate (lactic acid) - blood", "unit": "mmol/L", "id": 421}, {"sample_type": "Cerebrospinal fluid (CSF)", "test_type": "Metabolic Panel", "parameter": "Lactate (lactic acid) - CSF", "unit": "mg/dL", "id": 422}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Lactate (lactic acid) - PL", "unit": "mmol/L", "id": 423}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Lactate dehydrogenase (LDH) - blood", "unit": "U/L", "id": 424}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Leucine - blood", "unit": "micromol/L", "id": 425}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Lipase - blood", "unit": "U/L", "id": 426}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Lipoprotein (a) - blood", "unit": "mg/dL", "id": 427}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Lysine - blood", "unit": "micromol/L", "id": 428}, {"sample_type": "Sweat", "test_type": "Metabolic Panel", "parameter": "Macroduct Sweat Conductivity", "unit": "mmol/l equiv. NaCl", "id": 429}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Magnesium (Mg) - blood", "unit": "mg/dL", "id": 430}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Methionine - blood", "unit": "micromol/l", "id": 431}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Methyl histidine - 1 - blood", "unit": "micromol/l", "id": 432}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Methyl histidine - 3 - blood", "unit": "micromol/l", "id": 433}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Norepinephrine - blood", "unit": "pg/ml", "id": 434}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Octadecanoylcarnitine, quantitative - blood", "unit": "micromol/L", "id": 435}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Octanoylcarnitine - blood", "unit": "micromol/L", "id": 436}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Ornithine - blood", "unit": "micromol/L", "id": 437}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Osmolality - blood", "unit": "mOsm/kg", "id": 438}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Phenylalanine - blood", "unit": "micromol/L", "id": 439}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Phosohoethanolamine - blood", "unit": "micromol/L", "id": 440}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Phosphorus (Phos) - blood", "unit": "mg/dL", "id": 441}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Phosphoserine - blood", "unit": "micromol/L", "id": 442}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Potassium (K) - blood", "unit": "mmol/L", "id": 443}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Prealbumin - blood", "unit": "mg/dL", "id": 444}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Primidone (mysoline) - blood", "unit": "microg/ml", "id": 445}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Proline - blood", "unit": "micromol/l", "id": 446}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Propinoylcarnitine - blood", "unit": "micromol/l", "id": 447}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Protein total (TP) - blood", "unit": "gr/L", "id": 448}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Pyruvate - whole blood", "unit": "mmol/L", "id": 449}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Sarcosine - blood", "unit": "micromol/L", "id": 450}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Serine - blood", "unit": "micromol/L", "id": 451}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Serotonin - platelet poor plasma", "unit": "ng/ml", "id": 452}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Serotonin - Serum", "unit": "ng/ml", "id": 453}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Sodium (Na) - blood", "unit": "mmol/L", "id": 455}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Taurine - blood", "unit": "micromol/L", "id": 456}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Tetradecenoylcarnitine - blood", "unit": "micromol/L", "id": 457}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Threonine - blood", "unit": "micromol/L", "id": 458}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Transferrin (Trans) - blood", "unit": "mg/dL", "id": 459}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Transferrin saturation (TS) - blood", "unit": "%", "id": 460}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Tryptophan - blood", "unit": "micromol/L", "id": 461}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Tyrosine - blood", "unit": "micromol/L", "id": 462}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Urea nitrogen (BUN) - blood", "unit": "mg/dL", "id": 463}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Uric acid (UA) - blood", "unit": "mg/dL", "id": 464}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Valine - blood", "unit": "micromol/L", "id": 465}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Zinc - blood", "unit": "microg/dl", "id": 466}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Amylase - urine", "unit": "U/L", "id": 467}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Amylase / 24h - urine", "unit": "U/24h", "id": 468}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Amylase / Creatinine Clearance", "unit": "%", "id": 469}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Bilirubin - urine stick", "unit": "mg/dL", "id": 470}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Blood (BLD U. HGB) - urine stick", "unit": "mg/dL", "id": 471}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Calcium - urine (Ca U)", "unit": "mg/dL", "id": 472}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Calcium - urine/ 24 hours (Ca U/24h)", "unit": "mg/24hr", "id": 473}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Chloride - urine 24hr", "unit": "mmol/24hr", "id": 474}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Copper - urine", "unit": "microg/dl", "id": 475}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Copper 24h - urine", "unit": "microg/24h", "id": 476}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Creatinine - urine", "unit": "mg/dL", "id": 477}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Creatinine 24h - urine", "unit": "mg/24h", "id": 478}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Creatinine Clearance test", "unit": "ml/min", "id": 479}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Dopamine 24h - urine", "unit": "microg/24h", "id": 480}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Dopamine/ creatinine - urine", "unit": "microg/gr", "id": 481}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Epinephrine 24h", "unit": "microg/24h", "id": 482}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Glucose - urine stick", "unit": "mg/dL", "id": 483}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Homovanillic acid - urine", "unit": "mg/dL", "id": 484}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Homovanillic acid / creatinine", "unit": "mg/gr", "id": 485}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Homovanillic acid 24h - urine", "unit": "mg/24h", "id": 486}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Hydroxyindol acetic acid 5, 24h - urine", "unit": "mg/24h", "id": 487}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Ketone bodies - urine stick", "unit": "mg/dL", "id": 488}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Leukocytes - urine stick", "unit": "leu/?L", "id": 489}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Magnesium - urine", "unit": "mg/dL", "id": 490}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Magnesium 24h - urine", "unit": "mg/24h", "id": 491}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Microalbumin - urine", "unit": "mg/L", "id": 492}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Microalbumin 24h - urine", "unit": "mg/24h", "id": 493}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Microalbumin/Creatinine - urine", "unit": "mg/mg", "id": 494}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Nitrite - urine stick", "unit": "", "id": 495}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Norepinephrine - urine", "unit": "microg", "id": 496}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Norepinephrine - urine / creatinine random sample", "unit": "microg/gr", "id": 497}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Norepinephrine 24h - urine", "unit": "microg/24h", "id": 498}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Osmolarity - urine", "unit": "mOsm/kg", "id": 499}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "pH - urine stick", "unit": "", "id": 500}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Phosphorus - urine", "unit": "mg/dL", "id": 501}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Phosphorus 24h - urine", "unit": "mg/24h", "id": 502}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Potassium - urine", "unit": "mmol/L", "id": 503}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Potassium 24h - urine", "unit": "mmol/24h", "id": 504}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Protein - urine stick", "unit": "mg/dL", "id": 505}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Protein 24h - urine", "unit": "mg/24h", "id": 506}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Protein total - urine", "unit": "mg/dL", "id": 507}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Protein total - urine / creatitine", "unit": "mg/mg creatinine", "id": 508}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Sodium - urine", "unit": "mmol/L", "id": 509}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Sodium 24h - urine", "unit": "mmol/24h", "id": 510}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Specific Gravity - urine stick", "unit": "", "id": 511}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Urea - urine", "unit": "mg/dL", "id": 512}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Urea Nitrogen (BUN) - urine", "unit": "mg/dl", "id": 513}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Urea Nitrogen (BUN) 24h - urine", "unit": "g/24h", "id": 514}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Uric Acid - urine", "unit": "mg/dL", "id": 515}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Uric Acid 24h - urine", "unit": "mg/24h", "id": 516}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Urobilinogen - urine stick", "unit": "mg/dL", "id": 517}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Vanillylmandelic acid (VMA) / Creatinine - urine", "unit": "mg/gr", "id": 518}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Vanillylmandelic acid (VMA) 24h - urine", "unit": "mg/24h", "id": 519}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Zinc - urine", "unit": "microg/dl", "id": 520}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Zinc 24h - urine", "unit": "microg/24h", "id": 521}, {"sample_type": "<PERSON><PERSON>", "test_type": "<PERSON><PERSON>", "parameter": "Eosinophils - urine", "unit": "%", "id": 522}, {"sample_type": "Blood", "test_type": "Vitamins", "parameter": "Vitamin A Retinol (VitA) - blood", "unit": "microg/100ml", "id": 523}, {"sample_type": "Blood", "test_type": "Vitamins", "parameter": "Vitamin B1 (Thiamine) - blood", "unit": "ng/ml", "id": 524}, {"sample_type": "Blood", "test_type": "Vitamins", "parameter": "Vitamin B12 cyanocobolamin (B12) - blood", "unit": "pg/ml", "id": 525}, {"sample_type": "Blood", "test_type": "Vitamins", "parameter": "Vitamin B6 (pyridoxine) - blood", "unit": "ng/ml", "id": 526}, {"sample_type": "Blood", "test_type": "Vitamins", "parameter": "Vitamin D 1,25", "unit": "pg/ml", "id": 527}, {"sample_type": "Blood", "test_type": "Vitamins", "parameter": "Vitamin D3 Calcifediol (VitD3) , 25-Hydroxy - blood", "unit": "ng/ml", "id": 528}, {"sample_type": "Blood", "test_type": "Vitamins", "parameter": "Vitamin E alfa-Tocopherol (VitE) - blood", "unit": "mg/L", "id": 529}, {"sample_type": "Blood", "test_type": "Cardiac Biomarkers", "parameter": "Risk factor for myocardial infarction", "unit": "", "id": 530}, {"sample_type": "Blood", "test_type": "Complete Blood Count (CBC)", "parameter": "Unsaturated iron-binding capacity (UIBC)", "unit": "", "id": 531}, {"sample_type": "Blood", "test_type": "Complete Blood Count (CBC)", "parameter": "Total iron binding capacity", "unit": "", "id": 532}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Sex hormone binding globulin, serum", "unit": "nmol/L", "id": 533}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Urea - blood", "unit": "mg/dL", "id": 534}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Lactate", "unit": "mg/dL", "id": 535}, {"sample_type": "Blood", "test_type": "Complete Blood Count (CBC)", "parameter": "MCVr", "unit": "fL", "id": 536}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "alpha2-globulin % (EP) - blood", "unit": "", "id": 537}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "alpha2-globulin (EP) - blood", "unit": "", "id": 538}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Protein electrophoresis, total - blood", "unit": "g/L", "id": 539}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "M-Spike protein % - blood", "unit": "%", "id": 540}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "M-Spike protein - blood", "unit": "g/L", "id": 541}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Free Light chain Kappa - blood", "unit": "mg/L", "id": 542}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Free Light chain Lambda - blood", "unit": "mg/L", "id": 543}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Free Light chain Kappa/Lambda - blood", "unit": "ratio", "id": 544}, {"sample_type": "<PERSON><PERSON>", "test_type": "Bacterial culture", "parameter": "Bacterial culture", "unit": "", "id": 546}, {"sample_type": "Blood", "test_type": "Acid Fast <PERSON> (AFB) culture for Mycobacterium", "parameter": "Acid Fast <PERSON> (AFB) culture for Mycobacterium", "unit": "", "id": 547}, {"sample_type": "Blood", "test_type": "Bacterial culture", "parameter": "Bacterial culture", "unit": "", "id": 548}, {"sample_type": "Blood", "test_type": "Bacterial PCR", "parameter": "Bacterial PCR", "unit": "", "id": 549}, {"sample_type": "Blood", "test_type": "Blood parasite exam (Malaria)", "parameter": "Blood parasite exam (Malaria)", "unit": "", "id": 550}, {"sample_type": "Blood", "test_type": "Fungul culture", "parameter": "Fungul culture", "unit": "", "id": 551}, {"sample_type": "Blood", "test_type": "Viral PCR", "parameter": "Viral PCR", "unit": "", "id": 552}, {"sample_type": "Stool", "test_type": "Bacterial culture", "parameter": "Bacterial culture", "unit": "", "id": 553}, {"sample_type": "Stool", "test_type": "Bacterial PCR", "parameter": "Bacterial PCR", "unit": "", "id": 554}, {"sample_type": "Stool", "test_type": "Enteric Panel", "parameter": "Enteric Panel", "unit": "", "id": 555}, {"sample_type": "Stool", "test_type": "Ova and cyst (wet mount)", "parameter": "Ova and cyst (wet mount)", "unit": "", "id": 556}, {"sample_type": "Stool", "test_type": "Parasite exam", "parameter": "Parasite exam", "unit": "", "id": 557}, {"sample_type": "Stool", "test_type": "Pinworm exam", "parameter": "Pinworm exam", "unit": "", "id": 558}, {"sample_type": "Stool", "test_type": "Occult Blood", "parameter": "Occult Blood", "unit": "", "id": 559}, {"sample_type": "Throat", "test_type": "Bacterial culture", "parameter": "Bacterial culture", "unit": "", "id": 560}, {"sample_type": "Throat", "test_type": "Bacterial PCR", "parameter": "Bacterial PCR", "unit": "", "id": 561}, {"sample_type": "Throat", "test_type": "Viral PCR", "parameter": "Viral PCR", "unit": "", "id": 562}, {"sample_type": "Cerebrospinal fluid (CSF)", "test_type": "Bacterial culture", "parameter": "Bacterial culture", "unit": "", "id": 563}, {"sample_type": "Cerebrospinal fluid (CSF)", "test_type": "Bacterial PCR", "parameter": "Bacterial PCR", "unit": "", "id": 564}, {"sample_type": "Cerebrospinal fluid (CSF)", "test_type": "Fungul culture", "parameter": "Fungul culture", "unit": "", "id": 565}, {"sample_type": "Cerebrospinal fluid (CSF)", "test_type": "Viral PCR", "parameter": "Viral PCR", "unit": "", "id": 566}, {"sample_type": "Semen analysis", "test_type": "Semen analysis", "parameter": "Semen analysis", "unit": "", "id": 567}, {"sample_type": "Vaginal/ Cervical swab", "test_type": "Bacterial culture", "parameter": "Bacterial culture", "unit": "", "id": 568}, {"sample_type": "Vaginal/ Cervical swab", "test_type": "Bacterial PCR", "parameter": "Bacterial PCR", "unit": "", "id": 569}, {"sample_type": "Vaginal/ Cervical swab", "test_type": "Fungul culture", "parameter": "Fungul culture", "unit": "", "id": 570}, {"sample_type": "Vaginal/ Cervical swab", "test_type": "Vaginosis/Vaginitis Panel (Trichomonas, Yeast and Gardnerella)", "parameter": "Vaginosis/Vaginitis Panel (Trichomonas, Yeast and Gardnerella)", "unit": "", "id": 571}, {"sample_type": "Wound/ Ulcer swab", "test_type": "Bacterial culture", "parameter": "Bacterial culture", "unit": "", "id": 572}, {"sample_type": "Wound/ Ulcer swab", "test_type": "Bacterial PCR", "parameter": "Bacterial PCR", "unit": "", "id": 573}, {"sample_type": "Skin sample", "test_type": "Scabies exam", "parameter": "Scabies exam", "unit": "", "id": 574}, {"sample_type": "Nasopharyngeal swab", "test_type": "Antigen test", "parameter": "Antigen test", "unit": "", "id": 575}, {"sample_type": "Nasopharyngeal swab", "test_type": "Viral PCR", "parameter": "Viral PCR", "unit": "", "id": 576}, {"sample_type": "Nasopharyngeal swab", "test_type": "Bacterial culture", "parameter": "Bacterial culture", "unit": "", "id": 577}, {"sample_type": "Other sample", "test_type": "Abscess aspirate analysis", "parameter": "Abscess aspirate analysis", "unit": "", "id": 578}, {"sample_type": "Other sample", "test_type": "Bile analysis", "parameter": "Bile analysis", "unit": "", "id": 579}, {"sample_type": "Other sample", "test_type": "Bile", "parameter": "Bile", "unit": "", "id": 580}, {"sample_type": "Other sample", "test_type": "Bone marrow aspirate analysis", "parameter": "Bone marrow aspirate analysis", "unit": "", "id": 581}, {"sample_type": "Other sample", "test_type": "Breast milk analysis", "parameter": "Breast milk analysis", "unit": "", "id": 582}, {"sample_type": "Other sample", "test_type": "Catheter tips culture", "parameter": "Catheter tips culture", "unit": "", "id": 583}, {"sample_type": "Other sample", "test_type": "Ear swab analysis", "parameter": "Ear swab analysis", "unit": "", "id": 584}, {"sample_type": "Other sample", "test_type": "Eye swab analysis", "parameter": "Eye swab analysis", "unit": "", "id": 585}, {"sample_type": "Other sample", "test_type": "Synovial fluid (joint aspiration) analysis", "parameter": "Synovial fluid (joint aspiration) analysis", "unit": "", "id": 586}, {"sample_type": "Other sample", "test_type": "Pericardial fluid analysis", "parameter": "Pericardial fluid analysis", "unit": "", "id": 587}, {"sample_type": "Other sample", "test_type": "Pleaural fluid analysis", "parameter": "Pleaural fluid analysis", "unit": "", "id": 588}, {"sample_type": "Other sample", "test_type": "Peritoneal fuid analysis", "parameter": "Peritoneal fuid analysis", "unit": "", "id": 589}, {"sample_type": "Other sample", "test_type": "Pilonidal cyst anlysis", "parameter": "Pilonidal cyst anlysis", "unit": "", "id": 590}, {"sample_type": "Other sample", "test_type": "Sputum analysis", "parameter": "Sputum analysis", "unit": "", "id": 591}, {"sample_type": "Other sample", "test_type": "<PERSON><PERSON><PERSON><PERSON> discharde", "parameter": "<PERSON><PERSON><PERSON><PERSON> discharde", "unit": "", "id": 592}, {"sample_type": "Other sample", "test_type": "Bronchoalveolar lavage (BAL)", "parameter": "Bronchoalveolar lavage (BAL)", "unit": "", "id": 593}, {"sample_type": "Blood", "test_type": "FACS", "parameter": "Total B-cells (CD19+) (cells/ul)", "unit": "cells/ul", "id": 594}, {"sample_type": "Blood", "test_type": "FACS", "parameter": "Abnormal CD19 population of the PB-WBC", "unit": "", "id": 595}, {"sample_type": "Blood", "test_type": "FACS", "parameter": "Monoclonal B-cell lymphocytosis co-expressing CD5+CD19+ (cells/ul)", "unit": "cells/ul", "id": 596}, {"sample_type": "Blood", "test_type": "FACS", "parameter": "Lymph gate", "unit": "", "id": 597}, {"sample_type": "Blood", "test_type": "FACS", "parameter": "CD3", "unit": "", "id": 598}, {"sample_type": "Blood", "test_type": "FACS", "parameter": "CD4", "unit": "", "id": 599}, {"sample_type": "Blood", "test_type": "FACS", "parameter": "CD8", "unit": "", "id": 600}, {"sample_type": "Blood", "test_type": "FACS", "parameter": "CD19", "unit": "", "id": 601}, {"sample_type": "Blood", "test_type": "FACS", "parameter": "CD5", "unit": "", "id": 602}, {"sample_type": "Blood", "test_type": "FACS", "parameter": "CD20", "unit": "", "id": 603}, {"sample_type": "Blood", "test_type": "FACS", "parameter": "CD23", "unit": "", "id": 604}, {"sample_type": "Blood", "test_type": "FACS", "parameter": "CD79B", "unit": "", "id": 605}, {"sample_type": "Blood", "test_type": "FACS", "parameter": "CD200", "unit": "", "id": 606}, {"sample_type": "Blood", "test_type": "FACS", "parameter": "CD22", "unit": "", "id": 607}, {"sample_type": "Blood", "test_type": "FACS", "parameter": "CD43", "unit": "", "id": 608}, {"sample_type": "Blood", "test_type": "FACS", "parameter": "S.<PERSON>", "unit": "", "id": 609}, {"sample_type": "Blood", "test_type": "FACS", "parameter": "S.Kappa", "unit": "", "id": 610}, {"sample_type": "Blood", "test_type": "FACS", "parameter": "CD phenotype", "unit": "-", "id": 611}, {"sample_type": "Blood", "test_type": "Virology", "parameter": "Anti SARS-CoV-2 S protein", "unit": "U/mL", "id": 612}, {"sample_type": "Blood", "test_type": "Virology", "parameter": "Anti-SARS-Cov-2 N protein", "unit": "", "id": 613}, {"sample_type": "Blood", "test_type": "Complete Blood Count (CBC)", "parameter": "LUC Abs", "unit": "10*3/uL", "id": 614}, {"sample_type": "Blood", "test_type": "Complete Blood Count (CBC)", "parameter": "RDW-SD", "unit": "fL", "id": 615}, {"sample_type": "Blood", "test_type": "Complete Blood Count (CBC)", "parameter": "IMMATURE PLATELET FRACTION", "unit": "%", "id": 616}, {"sample_type": "Blood", "test_type": "Complete Blood Count (CBC)", "parameter": "IMMATURE PLATELET FRACTION, ABSOLUTE", "unit": "10*3/uL", "id": 617}, {"sample_type": "Blood", "test_type": "Complete Blood Count (CBC)", "parameter": "GRANULOCYTES, IMMATURE %", "unit": "%", "id": 618}, {"sample_type": "Blood", "test_type": "Complete Blood Count (CBC)", "parameter": "GRANULOCYTES IMMATURE , ABSOLUTE", "unit": "10*3/uL", "id": 619}, {"sample_type": "Blood", "test_type": "lipid profile", "parameter": "cholesterol/ HDL ratio", "unit": "", "id": 620}, {"sample_type": "Blood", "test_type": "lipid profile", "parameter": "LDL/HDL ratio", "unit": "", "id": 621}, {"sample_type": "Blood", "test_type": "lipid profile", "parameter": "non HDL cholesterol", "unit": "mg/dl", "id": 622}, {"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "Glucose, ABG", "unit": "mg/dl", "id": 623}, {"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "TEMPERATURE, ABG", "unit": "F", "id": 624}, {"sample_type": "Blood", "test_type": "Blood Gas", "parameter": "FIO2, ABG", "unit": "%", "id": 625}, {"sample_type": "Blood", "test_type": "Blood Type", "parameter": "ABO and RH", "unit": "", "id": 626}, {"sample_type": "Blood", "test_type": "lipid profile", "parameter": "LDL particle number", "unit": "nmol/L", "id": 627}, {"sample_type": "Blood", "test_type": "lipid profile", "parameter": "LDL-P small", "unit": "nmol/L", "id": 628}, {"sample_type": "Blood", "test_type": "lipid profile", "parameter": "LDL medium", "unit": "nmol/L", "id": 629}, {"sample_type": "Blood", "test_type": "lipid profile", "parameter": "HDL large", "unit": "nmol/L", "id": 630}, {"sample_type": "Blood", "test_type": "lipid profile", "parameter": "LDL pattern", "unit": "-", "id": 631}, {"sample_type": "Blood", "test_type": "lipid profile", "parameter": "LDL peak size", "unit": "Angstorm", "id": 632}, {"sample_type": "Blood", "test_type": "lipid profile", "parameter": "Apolipoprotein B", "unit": "mg/dl", "id": 633}, {"sample_type": "Blood", "test_type": "lipid profile", "parameter": "Lopoprotein A", "unit": "nmol/L", "id": 634}, {"sample_type": "Blood", "test_type": "lipid profile", "parameter": "Lp-PLA2", "unit": "nmol/min/mL", "id": 635}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Calcium/Creatinine Collect Urine", "unit": "mg/gr creat.", "id": 636}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "PT", "unit": "%", "id": 637}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Collagen Cross-Linked", "unit": "nM BCE/mM creat", "id": 638}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "BUN/<PERSON><PERSON><PERSON>ine Ratio", "unit": "", "id": 639}, {"sample_type": "Blood", "test_type": "lipid profile", "parameter": "VLDL", "unit": "mg/dl", "id": 640}, {"sample_type": "Blood", "test_type": "lipid profile", "parameter": "Non-HDL Cholesterol", "unit": "mg/dl", "id": 641}, {"sample_type": "Blood", "test_type": "Coagulation panel", "parameter": "PT", "unit": "%", "id": 642}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "SARS-CoV-2 IgG, semiquantitative", "unit": "", "id": 643}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "IL-1B", "unit": "pg/ml", "id": 644}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "IL-6", "unit": "pg/ml", "id": 645}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "IL-8", "unit": "pg/ml", "id": 646}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "IL-10", "unit": "pg/ml", "id": 647}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "sIL-2R", "unit": "u/ml", "id": 648}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "TNFa", "unit": "pg/ml", "id": 649}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "PT %", "unit": "%", "id": 650}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "CD2 (T-ER+, T-cell, NK) %", "unit": "%", "id": 651}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "DNT-cells (CD3+CD4-CD8-)", "unit": "%", "id": 652}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "CD5 (mature-T)", "unit": "%", "id": 653}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "CD25 (IL-2 receptor, activated T-B-cell)", "unit": "%", "id": 654}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "CD45RA", "unit": "%", "id": 655}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "CD45RO", "unit": "%", "id": 656}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "CD27", "unit": "%", "id": 657}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "CD127", "unit": "%", "id": 658}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "CD56/CD16", "unit": "%", "id": 659}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "CD19 %", "unit": "%", "id": 660}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "CD19/CD5", "unit": "%", "id": 661}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "CD20", "unit": "%", "id": 662}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "CD22", "unit": "%", "id": 663}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "CD23", "unit": "%", "id": 664}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "CD38+CD45+", "unit": "%", "id": 665}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Somatomedin", "unit": "ng/ml", "id": 666}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "<PERSON><PERSON>", "unit": "", "id": 667}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Helicobacter <PERSON>", "unit": "", "id": 668}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Interleukin 6", "unit": "pg/ml", "id": 669}, {"sample_type": "Blood", "test_type": "lipid profile", "parameter": "non-HDL cholesterol", "unit": "mg/dl", "id": 670}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "IGF-BP3", "unit": "mg/l", "id": 671}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "FMC7", "unit": "%", "id": 672}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "CD200", "unit": "%", "id": 673}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "HLA-DR", "unit": "%", "id": 674}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Pancreatic polypeptide", "unit": "pmol/l", "id": 675}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Glucagon", "unit": "pmol/l", "id": 676}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Vasoactive intestinal polypeptide", "unit": "pmol/l", "id": 677}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Albumin/creatinine ratio", "unit": "mg/g creat.", "id": 678}, {"sample_type": "Blood", "test_type": "Hematology", "parameter": "<PERSON><PERSON>s direct", "unit": "", "id": 679}, {"sample_type": "<PERSON><PERSON>", "test_type": "Urine Chemistry", "parameter": "Erythrocytes", "unit": "", "id": 680}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "LD1/LD2 ratio - electrophoresis", "unit": "", "id": 681}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Endomysial IgA Ab", "unit": "", "id": 682}, {"sample_type": "Blood", "test_type": "Metals", "parameter": "Mercury", "unit": "ug/L", "id": 683}, {"sample_type": "Blood", "test_type": "Metals", "parameter": "Arsenic", "unit": "ug/L", "id": 684}, {"sample_type": "Blood", "test_type": "Metals", "parameter": "Aluminum", "unit": "ug/L", "id": 685}, {"sample_type": "Blood", "test_type": "Metals", "parameter": "Lead", "unit": "ug/dl", "id": 686}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "AST/ALT ratio", "unit": "", "id": 687}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "BUN / <PERSON><PERSON><PERSON>ine Ratio", "unit": "", "id": 688}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "hepatitis A Ab total", "unit": "", "id": 689}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "HIV-1/HIV-2 Ag/Ab", "unit": "", "id": 690}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "rapid plasma reagin RPR", "unit": "", "id": 691}, {"sample_type": "Blood", "test_type": "Metabolic panel", "parameter": "ESR", "unit": "mm/1h", "id": 692}, {"sample_type": "Stool", "test_type": "Immunology", "parameter": "Cryptosporidium Ag", "unit": "", "id": 693}, {"sample_type": "Stool", "test_type": "Immunology", "parameter": "Clostridium Difficile toxins A & B", "unit": "", "id": 694}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "neutrophils cytoplasmic Ab, ANCA, C", "unit": "", "id": 695}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "neutrophils cytoplasmic Ab, ANCA, P", "unit": "", "id": 696}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "smooth muscle Ab", "unit": "", "id": 697}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "mitochondrial Ab", "unit": "", "id": 698}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "parietal cell Ab", "unit": "", "id": 699}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "cytomegalovirus CMV IgG", "unit": "", "id": 700}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "cytomegalovirus CMV IgM", "unit": "", "id": 701}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "EBV IgG", "unit": "", "id": 702}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "EBC IgM", "unit": "", "id": 703}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "alkaline phosphatase isoenzyme, placental 1", "unit": "U/L", "id": 704}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "alkaline phosphatase isoenzyme, atypical", "unit": "U/L", "id": 705}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "lactate dehydrogenase (LD) atypical (IgG complex", "unit": "U/L", "id": 706}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Antinuclear Ab (ANA)", "unit": "", "id": 707}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "coxsackievirus Ab-B", "unit": "", "id": 708}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "entero-echo virus", "unit": "", "id": 709}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "influenza A", "unit": "", "id": 710}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "influenza B", "unit": "", "id": 711}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "adenovirus Ab", "unit": "", "id": 712}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "C-ANCA", "unit": "", "id": 713}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "P-ANCA", "unit": "", "id": 714}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Kappa Light Chain", "unit": "mg/dl", "id": 715}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Lambda Light Chain", "unit": "mg/dl", "id": 716}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "kappa-lambda ratio", "unit": "", "id": 717}, {"sample_type": "Blood", "test_type": "Virology", "parameter": "Herpes IgG type 1 ", "unit": "-", "id": 727}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Bone-specific alkaline phosphatase (BAP)", "unit": "U/L", "id": 728}, {"sample_type": "Stool", "test_type": "Immunology", "parameter": "Calprotectin", "unit": "mg/kg", "id": 730}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Thyroid, Peroxidase (Microsomal) Ab - blood", "unit": "U/ml", "id": 732}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "Thyroglobulin, Ab - blood", "unit": "U/ml", "id": 733}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "free T3", "unit": "pmol/L", "id": 734}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "ANTI-Insulin", "unit": "-", "id": 735}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Creatinine, blood, enzymatic method", "unit": "mg/dl", "id": 736}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "ANTI-GAD", "unit": "IU/ml", "id": 737}, {"sample_type": "Blood", "test_type": "Endocrinology", "parameter": "T4, free", "unit": "pmol/l", "id": 738}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Anti Zinc Transporter 8", "unit": "U/ml", "id": 739}, {"sample_type": "Blood", "test_type": "Complete Blood Count (CBC)", "parameter": "PCT", "unit": "%", "id": 740}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "EBV-EBNA IgG Ab.", "unit": "-", "id": 741}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "EBV-IgM Ab.", "unit": "-", "id": 742}, {"sample_type": "Blood", "test_type": "Metabolic Panel", "parameter": "Anion gap", "unit": "mmol/L", "id": 777}, {"sample_type": "Blood", "test_type": "Immunology", "parameter": "Immunofixation, summary - blood", "unit": "-", "id": 545}]}