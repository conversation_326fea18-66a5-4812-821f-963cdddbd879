[Unit]
Description=Restart Docker containers for OCR
After=docker.service

[Service]
Type=oneshot
# Explicitly set user/group and environment
User=ec2-user
Group=docker
Environment="HOME=/home/<USER>"
Environment="PATH=/usr/local/bin:/usr/bin:/bin"
WorkingDirectory=/home/<USER>/ocr
# Use shell to handle commands sequentially
ExecStart=/bin/sh -c "/usr/local/bin/docker-compose down && /usr/local/bin/docker-compose up -d"

[Install]
WantedBy=multi-user.target