services:
  app:
    image: 087264100053.dkr.ecr.eu-central-1.amazonaws.com/mediboard-ocr-llm:latest
    ports:
      - "8000"
    env_file:
      - .env
    volumes:
      - /var/www/uploads:/app/uploaded_files
    logging:
      driver: awslogs
      options:
        awslogs-region: eu-central-1
        awslogs-group: /aws/mediboard/ocr/service/logs
        awslogs-stream: mediboard-ocr-llm-service
    networks:
      - app-network
    deploy:
      replicas: 1
      labels:
        - container="ocr"
        - name="app"
      resources:
        limits:
          cpus: 14
          memory: 32G
        reservations:
          cpus: 8
          memory: 24G

  redis:
    image: redis:5.0-bullseye
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 10s
    logging:
      driver: awslogs
      options:
        awslogs-region: eu-central-1
        awslogs-group: /aws/mediboard/ocr/redis/logs
        awslogs-stream: mediboard-ocr-llm-redis
    networks:
      - app-network
      - monitoring-network

  nginx:
    image: nginx:latest
    ports:
      - 80:80
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      app:
        condition: service_started
    logging:
      driver: awslogs
      options:
        awslogs-region: eu-central-1
        awslogs-group: /aws/mediboard/ocr/nginx/logs
        awslogs-stream: mediboard-ocr-llm-nginx
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 10s
    networks:
      - app-network
      - monitoring-network

  prometheus:
    image: prom/prometheus:latest
    ports:
      - "35761:9090"
    volumes:
      - ./prometheus:/etc/prometheus
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=30d'
      - '--storage.tsdb.retention.size=8GB'
      - '--storage.tsdb.wal-compression' 
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
    networks:
      - monitoring-network

  node-exporter:
    image: prom/node-exporter:latest
    ports:
      - "35763:9100"
    restart: unless-stopped
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
      - '--collector.filesystem'
      - '--collector.diskstats'
    networks:
      - monitoring-network

  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    restart: unless-stopped
    ports:
      - "35764:8080"
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    networks:
      - monitoring-network

  grafana:
    image: grafana/grafana:latest
    ports:
      - "35762:3000"
    restart: unless-stopped
    depends_on:
      - prometheus
    volumes:
      - ./grafana/provisioning:/etc/grafana/provisioning
      - ./grafana/dashboards:/etc/grafana/dashboards
      - grafana_data:/var/lib/grafana
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin_password
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_DASHBOARDS_DEFAULT_HOME_DASHBOARD_PATH=/etc/grafana/dashboards/node_exporter/node_exporter.json
    networks:
      - monitoring-network

  nginx-exporter:
    image: nginx/nginx-prometheus-exporter:latest
    ports:
      - "35765:9113"
    command: ["-nginx.scrape-uri", "http://nginx/status"]
    depends_on:
      - nginx
    networks:
      - monitoring-network

  redis-exporter:
    image: oliver006/redis_exporter:latest
    restart: unless-stopped
    command:
      - '--redis.addr=redis://redis:6379'
      - '--web.listen-address=:9121'
    environment:
      - TZ=UTC
    ports:
      - "35766:9121"
    networks:
      - monitoring-network
    depends_on:
      - redis

  alertmanager:
    image: prom/alertmanager:latest
    restart: unless-stopped
    volumes:
      - ./alertmanager/alertmanager.yml:/etc/alertmanager/alertmanager.yml
    ports:
      - "35767:9093"
    depends_on:
      - prometheus
    networks:
      - monitoring-network

networks:
  app-network:
    driver: bridge
  monitoring-network:
    driver: bridge

volumes:
  prometheus_data:
  grafana_data:
