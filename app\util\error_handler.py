import os
import sys

def handle_error(err):
    exc_type, exc_obj, exc_tb = sys.exc_info()
    fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
    sys.stdout.flush()
    print("Error details {}".format(str({"exc_type":exc_type, "fname":fname, "tb_lineno":exc_tb.tb_lineno, "err":err})))


import traceback
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

def get_error_details(error: Exception) -> Dict[str, Any]:
    """
    Extract detailed information from an exception.
    
    Args:
        error: The exception to analyze
        
    Returns:
        Dict containing error details including:
        - error_type: The exception class name
        - message: The error message
        - traceback: Full traceback as a list of strings
        - line_number: Line number where error occurred
        - file_name: File where error occurred
        - function_name: Function where error occurred
    """
    try:
        # Get the traceback object
        tb = traceback.extract_tb(error.__traceback__)
        
        # Get the last frame (where the error occurred)
        last_frame = tb[-1] if tb else None
        
        error_details = {
            "error_type": error.__class__.__name__,
            "message": str(error),
            "traceback": traceback.format_exception(type(error), error, error.__traceback__),
            "line_number": last_frame.lineno if last_frame else None,
            "file_name": last_frame.filename if last_frame else None,
            "function_name": last_frame.name if last_frame else None
        }
        
        logger.error(
            f"🚨 ERROR REPORT 🚨\n"
            f"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n"
            f"📋 Type: {error_details['error_type']}\n"
            f"💬 Message: {error_details['message']}\n"
            f"📁 File: {error_details['file_name']}\n"
            f"📍 Line: {error_details['line_number']}\n" 
            f"⚡️ Function: {error_details['function_name']}\n"
            f"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        )
        
        return error_details
        
    except Exception as e:
        logger.error(f"Error while getting error details: {str(e)}")
        return {
            "error_type": "Unknown",
            "message": str(error),
            "traceback": [],
            "line_number": None,
            "file_name": None,
            "function_name": None
        }
