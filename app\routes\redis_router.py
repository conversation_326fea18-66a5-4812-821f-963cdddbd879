from fastapi import APIRouter, HTTPException
from typing import Optional, Any, Dict
from app.model.ocr_model import (
    ReportStatus,
)
from pydantic import BaseModel
from app.services.redis_session_manager import RedisSessionManager

redis_router = APIRouter()

class HealthResponse(BaseModel):
    status: str
    details: Dict[str, str]

@redis_router.get("/redis")
async def check_redis_health() -> HealthResponse:
    """
    Check Redis connection health.
    Returns:
        HealthResponse: Status and details of Redis connection
    """
    try:
        # Create a temporary session manager to test Redis connection
        session_manager = RedisSessionManager("health-check")
        # Try to ping Redis
        session_manager.redis.ping()
        
        # Get Redis info
        info = session_manager.redis.info()
        
        return HealthResponse(
            status="healthy",
            details={
                "connected_clients": str(info.get("connected_clients", "N/A")),
                "used_memory_human": info.get("used_memory_human", "N/A"),
                "redis_version": info.get("redis_version", "N/A")
            }
        )
    except Exception as e:
        raise HTTPException(
            status_code=503,
            detail={
                "status": "unhealthy",
                "message": f"Redis connection failed: {str(e)}"
            }
        )


@redis_router.post("/session/{session_id}")
def create_session(session_id: str, session_data: Optional[ReportStatus] = None):
    """Create a new session with optional initial data."""
    redis_manager = RedisSessionManager(session_id)
    
    # Get the session that was created in the constructor
    session = redis_manager.get_session()

    # If session data was provided, update the relevant fields
    if session_data:
        for key, value in session_data.dict().items():
            if value is not None:
                redis_manager.update_session_data(key, value)
        session = redis_manager.get_session()

    return session

@redis_router.get("/sessions")
def get_all_sessions():
    """Get a list of all available session IDs."""
    # Use an empty session ID since we just need access to the Redis connection
    redis_manager = RedisSessionManager("")
    sessions = redis_manager.get_all_sessions()
    return {"sessions": sessions}


@redis_router.get("/session/{session_id}")
def get_session(session_id: str):
    """Retrieve a session."""
    redis_manager = RedisSessionManager(session_id)
    session_data = redis_manager.get_session()
    if session_data is not None:
        return session_data
    else:
        raise HTTPException(status_code=404, detail="Session not found")


@redis_router.put("/session/{session_id}/data/{key}")
def update_session_data(session_id: str, key: str, data: Any):
    """Update a specific key in the session data.

    Args:
        session_id: ID of the session to update
        key: The key in the data dict to update
        data: The value to set for the key
    """
    redis_manager = RedisSessionManager(session_id)
    success = redis_manager.update_session_data(key, data)
    if success:
        return {"status": "Session data updated successfully"}
    else:
        raise HTTPException(status_code=500, detail="Failed to update session data")


@redis_router.delete("/session/{session_id}")
def delete_session(session_id: str):
    """Delete a session."""
    redis_manager = RedisSessionManager(session_id)
    success = redis_manager.delete_session()
    if success:
        return {"status": "Session deleted successfully"}
    else:
        raise HTTPException(status_code=500, detail="Failed to delete session")
