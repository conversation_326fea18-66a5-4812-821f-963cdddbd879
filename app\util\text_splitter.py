def split_text_with_count(text: str) -> tuple[list[str], int]:
    """
    Split text into an array by newlines and return the array with its count.

    Args:
        text (str): Input text to split

    Returns:
        tuple[list[str], int]: Tuple containing the array of split text and count of lines
    """
    if not text:
        return [], 0

    lines = text.split("#")
    return lines, len(lines)

