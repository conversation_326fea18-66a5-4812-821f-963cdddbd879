from typing import Dict, List, Any, Optional
from langchain_openai import OpenAIEmbeddings
from langchain_nomic import NomicEmbeddings
from langchain_community.vectorstores import FAISS
from app.util.logger import logger
from enum import Enum
import json
import os


class VectorDataType(Enum):
    DOCTORS = 'doctors'
    INSTITUTIONS = 'institutions'
    LAB_REPORTS = 'lab_reports'


class VectorDataManager:
    # Static storage for vectorized data
    _vector_stores: Dict[str, FAISS] = {
        VectorDataType.DOCTORS.value: None,
        VectorDataType.INSTITUTIONS.value: None,
        VectorDataType.LAB_REPORTS.value: None
    }

    # Static storage for raw data
    _raw_data: Dict[str, List[Dict[str, Any]]] = {
        VectorDataType.DOCTORS.value: [],
        VectorDataType.INSTITUTIONS.value: [],
        VectorDataType.LAB_REPORTS.value: [],
    }

    @classmethod
    def _vectorize_data(cls, data_type: VectorDataType, data: List[Dict[str, Any]]) -> FAISS:
        """Helper method to vectorize data"""
        try:
            logger.info(f"Vectorizing data for {data_type}")
            documents = []
            metadatas = []
            
            for item in data:
                doc_text = " ".join([f"{k}={v}" for k,v in item.items()])
                documents.append(doc_text)
                metadatas.append(item)
                
            embeddings = None

            try:
                logger.info("Trying OpenAI embeddings...")
                embeddings = OpenAIEmbeddings()
                # Test embedding a dummy query to check if the API is functional
                embeddings.embed_query("test")  
            except Exception as e:
                logger.warning(f"OpenAI embedding failed: {str(e)}. Falling back to Nomic embeddings...")
                embeddings = NomicEmbeddings(model="nomic-embed-text-v1.5")

            vector_store = FAISS.from_texts(
                documents,
                embeddings,
                metadatas=metadatas
            )
            
            # Cache the vector store and raw data
            cls._vector_stores[data_type.value] = vector_store
            cls._raw_data[data_type.value] = data
            
            return vector_store
            logger.info(f"Successfully vectorized data for {data_type}")

        except Exception as e:
            logger.error(f"Error vectorizing data for {data_type}: {str(e)}")
            raise

    @classmethod
    def get_vectorized_data(cls, data_type: VectorDataType, data: Optional[List[Dict[str, Any]]] = None) -> FAISS:
        """
        Returns vectorized data for the specified type. Uses cached vector store if available.
        
        Args:
            data_type: Type of data (VectorDataType enum)
            data: Optional data to vectorize if vector store doesn't exist
        """
        if data_type.value not in cls._vector_stores:
            raise ValueError(f"Invalid data type: {data_type}")

        # Return existing vector store if available
        if cls._vector_stores[data_type.value] is not None:
            return cls._vector_stores[data_type.value]

        # If no data provided, try loading from ocr_data.json
        if data is None:
            ocr_data_path = os.path.join('static', 'ocr_data.json')
            try:
                with open(ocr_data_path, 'r') as f:
                    ocr_data = json.load(f)
                    data = ocr_data.get(data_type.value, [])
            except (FileNotFoundError, json.JSONDecodeError):
                data = []

        if not data:
            raise ValueError(f"No data available to vectorize for {data_type}")

        return cls._vectorize_data(data_type, data)

    @classmethod
    def update_data(cls, data_type: VectorDataType, data: Dict[str, Any]) -> bool:
        """
        Updates or adds new data and re-vectorizes the collection.
        
        Args:
            data_type: Type of data (VectorDataType enum)
            data: Dictionary containing the data to update/add
        """
        try:
            if data_type.value not in cls._raw_data:
                raise ValueError(f"Invalid data type: {data_type}")

            # Check if item with same ID exists
            item_id = data.get('id')
            if item_id is None:
                existing_ids = [item.get('id', 0) for item in cls._raw_data[data_type.value]]
                item_id = max(existing_ids, default=0) + 1
                data['id'] = item_id

            # Update raw data in memory
            updated = False
            for i, item in enumerate(cls._raw_data[data_type.value]):
                if item.get('id') == item_id:
                    cls._raw_data[data_type.value][i] = data
                    updated = True
                    break
            
            if not updated:
                cls._raw_data[data_type.value].append(data)

            # Update OCR data file
            ocr_data_path = os.path.join('static', 'ocr_data.json')
            try:
                with open(ocr_data_path, 'r') as f:
                    ocr_data = json.load(f)
            except (FileNotFoundError, json.JSONDecodeError):
                ocr_data = {}

            if data_type.value not in ocr_data:
                ocr_data[data_type.value] = []

            # Update or add in JSON file
            file_updated = False
            for i, item in enumerate(ocr_data[data_type.value]):
                if item.get('id') == item_id:
                    ocr_data[data_type.value][i] = data
                    file_updated = True
                    break

            if not file_updated:
                ocr_data[data_type.value].append(data)

            # Save updated data back to file
            with open(ocr_data_path, 'w') as f:
                json.dump(ocr_data, f, indent=2)

            # Re-vectorize the updated data
            cls._vectorize_data(data_type, cls._raw_data[data_type.value])

            logger.info(f"Successfully updated and re-vectorized {data_type} data")
            return True

        except Exception as e:
            logger.error(f"Error updating {data_type} data: {str(e)}")
            return False




# Example usage of VectorDataManager
def example_vector_usage():
    # Example 1: Update doctor data
    doctor_data = {
        "id": 123,
        "doctorName": "Dr. John Smith", 
        "title": "dr",
        "doctorLastName": "Smith"
    }
    VectorDataManager.update_data(VectorDataType.DOCTORS, doctor_data)

    # Example 2: Update institution data
    institution_data = {
        "id": 456,
        "name": "Central Hospital",
        "address": "123 Medical Drive"
    }
    VectorDataManager.update_data(VectorDataType.INSTITUTIONS, institution_data)

    # Example 3: Update lab test data
    lab_test_data = {
        "id": 789,
        "sample_type": "Blood",
        "test_type": "Complete Blood Count",
        "parameter": "Hemoglobin"
    }
    VectorDataManager.update_data(VectorDataType.LAB_REPORTS, lab_test_data)

    # Example 4: Get vectorized data for doctors
    doctors_vectorstore = VectorDataManager.get_vectorized_data(VectorDataType.DOCTORS)
    similar_doctors = doctors_vectorstore.similarity_search("Dr Smith cardiologist", k=2)
    
    # Example 5: Get vectorized data for institutions
    institutions_vectorstore = VectorDataManager.get_vectorized_data(VectorDataType.INSTITUTIONS) 
    similar_hospitals = institutions_vectorstore.similarity_search("central hospital", k=2)

    # Example 6: Get vectorized data for lab tests
    lab_tests_vectorstore = VectorDataManager.get_vectorized_data(VectorDataType.LAB_REPORTS)
    similar_tests = lab_tests_vectorstore.similarity_search("blood count hemoglobin", k=2)


