import os
from typing import Any, List, Optional, Dict
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, APIRouter, UploadFile, File, Form, BackgroundTasks
from pathlib import Path
from app.model.ocr_model import (
    ReportStatus,
    StageDataSummary,
    StageDataPhysician,
    StageDataInstitution,
    StageDataLabReport,
    StageReport,
    ProcessingStage,
    StageStatus,
)
from app.util.checks import OCR_ALLOWED_EXTENSIONS
from app.services.llm_services import LLMServices
import json
from app.util.error_handler import handle_error
from app.services.redis_session_manager import RedisSessionManager
from app.util.logger import logger
from app.util.vectors import VectorDataManager, VectorDataType
from app.services.webhook_service import WebhookService

# Initialize webhook service
webhook_service = WebhookService()


ocr_router = APIRouter(
    prefix="/ocr", responses={404: {"description": "Not found"}}, tags=["ocr"]
)


# Define the upload directory
UPLOAD_DIR = "uploaded_files"
os.makedirs(UPLOAD_DIR, exist_ok=True)


@ocr_router.post("/process")
async def process_document(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    language: str = Form("english"),
    test_id: str = Form(...),
    user_id: int = Form(...),
):

    logger.info("Starting document processing...")

    # Initialize Redis session
    redis_manager = RedisSessionManager(session_id=str(test_id))

    # Initialize session with user_id
    initial_data = ReportStatus(
        user_id=str(user_id),
        document_summary=StageDataSummary(
            progress=0, status=StageStatus.PENDING, data=None
        ),
        matched_physician=StageDataPhysician(
            progress=0, status=StageStatus.PENDING, data=None
        ),
        matched_institution=StageDataInstitution(
            progress=0, status=StageStatus.PENDING, data=None
        ),
        lab_reports=StageDataLabReport(progress=0, status=StageStatus.PENDING, data=[]),
        analysis=StageReport(stage=ProcessingStage.DOCUMENT_SUMMARY, message=""),
    )
    redis_manager.create_session(initial_data.dict())

    if not file:
        logger.error("No file provided")
        return {"status": False, "message": "No file provided"}

    file_path = Path(UPLOAD_DIR) / file.filename
    with open(file_path, "wb") as f:
        f.write(await file.read())

    logger.debug(f"File path: {file_path}")

    try:
        llm_service = LLMServices()

        # Create a wrapper function for background processing
        def process_document_background():
            try:
                # Update document summary stage status
                document_summary = StageDataSummary(
                    progress=0, status=StageStatus.IN_PROGRESS, data=None
                )
                redis_manager.update_session_data("document_summary", document_summary)

                # Get document summary
                file_content = llm_service.get_document_summary(
                    file_path=file_path, user_id=user_id, language=language
                )

                logger.info(f"Document summary results: {file_content}")

                if not file_content.get("status", False):
                    error_msg = file_content.get("message", "Unknown error")
                    logger.error(f"Error in processing document: {error_msg}")

                    # Update all stages to failed status
                    redis_manager.update_session_data(
                        "document_summary",
                        StageDataSummary(
                            progress=100, status=StageStatus.FAILED, data=None
                        ),
                    )
                    redis_manager.update_session_data(
                        "matched_physician",
                        StageDataPhysician(
                            progress=100, status=StageStatus.FAILED, data=None
                        ),
                    )
                    redis_manager.update_session_data(
                        "matched_institution",
                        StageDataInstitution(
                            progress=100, status=StageStatus.FAILED, data=None
                        ),
                    )
                    redis_manager.update_session_data(
                        "lab_reports",
                        StageDataLabReport(
                            progress=100, status=StageStatus.FAILED, data=[]
                        ),
                    )

                    # Notify error through webhook
                    webhook_service.notify_error(
                        test_id=test_id,
                        user_id=str(user_id),
                        error_message=error_msg,
                        stage="document_summary",
                    )

                    if os.path.exists(file_path):
                        os.remove(file_path)
                    return

                document_summary.progress = 100
                document_summary.status = StageStatus.COMPLETE

                # Add index to lab reports before updating session data
                lab_reports = file_content["data"].get("lab_reports", [])
                for i, report in enumerate(lab_reports):
                    report["index"] = i
                file_content["data"]["lab_reports"] = lab_reports
                document_summary.data = file_content["data"]
                redis_manager.update_session_data("document_summary", document_summary)

                if not file_content.get("data", {}).get("is_lab_report", False):
                    logger.warning("Uploaded file is not a lab report")
                    if os.path.exists(file_path):
                        os.remove(file_path)
                    return

                # Process OCR
                llm_service.process_ocr(
                    test_id=test_id,
                    user_id=user_id,
                    file_content=file_content,
                    language=language,
                )
            finally:
                # Clean up file after processing
                if os.path.exists(file_path):
                    os.remove(file_path)

        # Add the background processing task
        background_tasks.add_task(process_document_background)

        logger.info("Document processing started successfully")
        return {
            "status": True,
            "message": "Document processing started. Use the status endpoint to track progress.",
            "test_id": test_id
        }
    except Exception as e:
        logger.exception(e)
        # Update all stages to failed status
        redis_manager.update_session_data(
            "document_summary",
            StageDataSummary(progress=100, status=StageStatus.FAILED, data=None),
        )
        redis_manager.update_session_data(
            "matched_physician",
            StageDataPhysician(progress=100, status=StageStatus.FAILED, data=None),
        )
        redis_manager.update_session_data(
            "matched_institution",
            StageDataInstitution(progress=100, status=StageStatus.FAILED, data=None),
        )
        redis_manager.update_session_data(
            "lab_reports",
            StageDataLabReport(progress=100, status=StageStatus.FAILED, data=[]),
        )

        handle_error(e)
        if os.path.exists(file_path):
            os.remove(file_path)
        raise HTTPException(status_code=500, detail=str(e))


@ocr_router.get("/ocr/status/{user_id}")
async def get_ocr_progress(user_id: str):
    try:
        # Initialize Redis session manager with user ID as session ID
        redis_manager = RedisSessionManager(session_id=str(user_id))

        # Get session data
        session_data = redis_manager.get_session()

        if session_data:
            return session_data
        else:
            return {
                "status": False,
                "message": "No session found for the given user ID",
            }

    except Exception as e:
        return {"status": False, "message": str(e)}


@ocr_router.post("/update-vector/{data_type}")
async def update_vector_data(data_type: str, data: str = Form(...)):
    """
    Updates vector data for the specified data type.

    Args:
        data_type: Type of data ('doctors', 'institutions', or 'lab_reports')
        data: JSON string containing array of data items to update/add
    """
    try:
        # Parse JSON string to array
        try:
            parsed_data = json.loads(data)
            if not isinstance(parsed_data, list):
                raise HTTPException(
                    status_code=400, detail="Data must be an array of items"
                )
            if not all(isinstance(item, dict) for item in parsed_data):
                raise HTTPException(
                    status_code=400, detail="All items in array must be objects"
                )
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="Invalid JSON data provided")

        # Validate data type
        try:
            vector_type = VectorDataType(data_type)
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid data type. Must be one of: {[t.value for t in VectorDataType]}",
            )

        # Update vector data for each item
        success = True
        for item in parsed_data:
            if not VectorDataManager.update_data(vector_type, item):
                success = False
                break

        if success:
            return {"status": True, "message": f"Successfully updated {data_type} data"}
        else:
            raise HTTPException(
                status_code=500, detail=f"Failed to update {data_type} data"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(e)
        raise HTTPException(status_code=500, detail=str(e))
