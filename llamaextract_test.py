import json
import os
from datetime import datetime
from llama_cloud.types import ExtractConfig, ExtractMode
from llama_cloud_services import LlamaExtract
from app.model.ocr_model import (
    UploadedFileContent,
    MatchingPhysicianInfo,
    MatchMedicalFacility,
    LabReportItem,
)
from app.core.config import settings

from dotenv import load_dotenv
load_dotenv()

def ensure_directory(directory):
    """Create directory if it doesn't exist."""
    if not os.path.exists(directory):
        os.makedirs(directory)
        print(f"Created directory: {directory}")

def save_result(result, filename, output_dir):
    """Save extraction result to a JSON file with timestamp."""
    ensure_directory(output_dir)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    base_name = os.path.splitext(filename)[0]
    output_filename = os.path.join(output_dir, f"extraction_result_{base_name}_{timestamp}.json")
    with open(output_filename, 'w', encoding='utf-8') as f:
        json.dump(result.data, f, indent=4, ensure_ascii=False)
    return output_filename

def process_batch(files_batch, extractor, agent, input_dir, output_dir):
    """Process a batch of files and return results with timestamps."""
    results = []
    for file in files_batch:
        start_time = datetime.now()
        try:
            print(f"\nProcessing file: {file}")
            file_path = os.path.join(input_dir, file)
            result = agent.extract(file_path)
            output_file = save_result(result, file, output_dir)
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            
            print(f"Successfully processed {file} in {processing_time:.2f} seconds")
            print(f"Results saved to {output_file}")
            
            results.append({
                "file": file,
                "status": "success",
                "output": output_file,
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "processing_time_seconds": processing_time
            })
        except Exception as e:
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            print(f"Error processing {file}: {str(e)}")
            results.append({
                "file": file,
                "status": "failed",
                "error": str(e),
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "processing_time_seconds": processing_time
            })
    return results

def save_batch_summary(batch_results, batch_number, output_dir):
    """Save batch processing summary to a JSON file."""
    ensure_directory(output_dir)
    summary_file = os.path.join(output_dir, f"batch_{batch_number}_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(batch_results, f, indent=4)
    return summary_file


def main():
    # Define input and output directories
    input_dir = "files_2"
    output_dir = "extraction_results"
    ensure_directory(output_dir)
    # List of 10 files (using placeholder names for now)
    all_files = [
        # "20130717_mayo_clinic.pdf",
        "20150104_maccabi.pdf",
        # "20210214_maccabi.pdf",
        "20230426_labcorp.pdf",
        # "20230608_maccabi.pdf",
        "20240413_ichilov.pdf",
        "20240430_cedars_sinai.pdf",
        "20240724_sheba.pdf",
        # "20241015_ichilov.pdf",
        # "20241108_quest.pdf",
        "maccabi_20161228.pdf",
        # "quest_diagnostics_31052024.pdf",
        # "Shmuel Meitar Lab 29.12.23.pdf",
        # "20190110_maccabi.pdf",
        # "20240702_maccabi.pdf",
        # "20250109_sheba.pdf",
    ]

    print("Initializing LlamaExtract client...")
    extractor = LlamaExtract(api_key=settings.LLAMA_CLOUD_API_KEY)
    print("LlamaExtract client initialized successfully")

    lab_extraction = """
    You are a medical case manager tasked with processing a lab test report and extracting key information. Your goal is to retrieve necessary information for the lab test report data.

    Instructions:
    1. The user specified the lab test report is in this language: hebrew, use this for context awareness.
    2. Extract the relevant information and map it into the specified JSON structure.
    3. Ensure that the **result** field contains only the result value of a lab test parameter without its unit. If the result contains Hebrew text, translate it to English (e.g., 'לא בוצע' → 'Not performed').
    4. Place any accompanying unit of measurement into the **units** field separately.
    5. If the value and unit are combined (e.g., "120 mg/dL"), extract **120** into the **result** field and **mg/dL** into the **units** field.
    6. If the lab report contains a range, make sure it's not combined with the unit in the **range** field. If the range contains Hebrew text, translate it to English (e.g., 'קטן מ-200' → '>200').
    7. Ensure you identify any additional note or interpretation or comment or explanation associated with a lab test parameter from the doctor. If you find any, place it in the **comment** field. Please differentiate it from the result value of the parameter. Ensure you put the result value in the **result** field and put a comment or interpretation or explanation you find in the **comment** field.
    8. If the original comment field is in Hebrew, translate it to English and place the translation in the **comment_english** field.
    """

    config = ExtractConfig(system_prompt=lab_extraction, 
                        extraction_mode=ExtractMode.PREMIUM)
                        
    print("\nRetrieving existing extraction agent...")
    try:
        agent = extractor.get_agent("Mediboard LLM Extraction")
        print("Successfully retrieved existing agent")
    except Exception as e:
        print("Agent not found, creating new agent...")
        agent = extractor.create_agent(name="mediboard_lab_extraction",
                                    data_schema=UploadedFileContent,
                                    config=config)
        print("New agent created successfully")

    # Process files in batches of 3
    batch_size = 3
    all_batch_results = []
    
    for batch_num, i in enumerate(range(0, len(all_files), batch_size), 1):
        batch = all_files[i:i + batch_size]
        print(f"\nProcessing batch {batch_num} of {(len(all_files) + batch_size - 1) // batch_size}")
        print(f"Files in this batch: {', '.join(batch)}")
        
        batch_results = process_batch(batch, extractor, agent, input_dir, output_dir)
        summary_file = save_batch_summary(batch_results, batch_num, output_dir)
        print(f"Batch {batch_num} summary saved to {summary_file}")
        all_batch_results.extend(batch_results)

    # Calculate statistics and save overall summary
    successful_files = [r for r in all_batch_results if r["status"] == "success"]
    failed_files = [r for r in all_batch_results if r["status"] == "failed"]
    
    total_processing_time = sum(r["processing_time_seconds"] for r in all_batch_results)
    avg_processing_time = total_processing_time / len(all_batch_results) if all_batch_results else 0
    
    final_summary = {
        "execution_timestamp": datetime.now().isoformat(),
        "total_files": len(all_files),
        "total_batches": (len(all_files) + batch_size - 1) // batch_size,
        "statistics": {
            "successful": len(successful_files),
            "failed": len(failed_files),
            "success_rate": f"{(len(successful_files) / len(all_files) * 100):.2f}%",
            "total_processing_time_seconds": total_processing_time,
            "average_processing_time_seconds": avg_processing_time,
        },
        "results": all_batch_results
    }
    
    summary_filename = os.path.join(output_dir, f"extraction_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    with open(summary_filename, 'w', encoding='utf-8') as f:
        json.dump(final_summary, f, indent=4)
    
    print("\nExtraction process completed:")
    print(f"- Total files processed: {len(all_files)}")
    print(f"- Successful: {len(successful_files)}")
    print(f"- Failed: {len(failed_files)}")
    print(f"- Success rate: {(len(successful_files) / len(all_files) * 100):.2f}%")
    print(f"- Total processing time: {total_processing_time:.2f} seconds")
    print(f"- Average processing time per file: {avg_processing_time:.2f} seconds")
    print(f"\nDetailed summary saved to: {summary_filename}")

if __name__ == "__main__":
    main()
