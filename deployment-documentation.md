# Mediboard-LLM Deployment Documentation

## Overview

The Mediboard-LLM system is deployed as a containerized application on AWS infrastructure. The deployment uses Docker containers orchestrated with Docker Compose and is deployed through a GitHub Actions CI/CD pipeline. The system includes monitoring capabilities with Prometheus and Grafana.

## Deployment Architecture

### Infrastructure Components

1. **AWS EC2 Instance**
   - Hosts the Docker containers
   - Located in the eu-central-1 region
   - Instance accessible via public IP for deployment
   - Uses private IP for internal communication

2. **AWS ECR (Elastic Container Registry)**
   - Stores the Docker images
   - Repository name: mediboard-ocr-llm
   - Full image URL: `087264100053.dkr.ecr.eu-central-1.amazonaws.com/mediboard-ocr-llm:latest`

3. **AWS CloudWatch**
   - Collects logs from all containers
   - Log groups:
     - `/aws/mediboard/ocr/service/logs`
     - `/aws/mediboard/ocr/redis/logs`
     - `/aws/mediboard/ocr/nginx/logs`

4. **<PERSON>WS Secrets Manager**
   - Stores environment variables and secrets
   - Secret ID referenced in GitHub Actions workflow

### Application Components

The deployment consists of the following containerized services:

1. **Application Service (app)**
   - Custom Python FastAPI application
   - Image: `087264100053.dkr.ecr.eu-central-1.amazonaws.com/mediboard-ocr-llm:latest`
   - Exposed on port 8000 (internal)
   - Resource allocation:
     - CPU limits: 14 cores
     - Memory limits: 32GB
     - CPU reservations: 8 cores
     - Memory reservations: 24GB
   - Volume mount: `/var/www/uploads:/app/uploaded_files`

2. **Redis Service (redis)**
   - Image: `redis:5.0-bullseye`
   - Used for session management and caching
   - Health check configured with 30-second intervals

3. **Nginx Service (nginx)**
   - Image: `nginx:latest`
   - Exposed on port 80
   - Acts as a reverse proxy for the application
   - Configuration mounted from `./nginx.conf`
   - Health check configured with 30-second intervals
   - Client timeout settings:
     - `client_body_timeout`: 365 days
     - `client_header_timeout`: 365 days
     - `keepalive_timeout`: 365 days
   - Proxy timeout settings:
     - `proxy_read_timeout`: 365 days
     - `proxy_send_timeout`: 365 days
     - `proxy_connect_timeout`: 365 days

### Monitoring Stack

The deployment includes a comprehensive monitoring stack:

1. **Prometheus**
   - Image: `prom/prometheus:latest`
   - Exposed on port 35761
   - Configuration mounted from `./prometheus`
   - Data retention: 30 days or 8GB
   - Persistent volume for data storage

2. **Node Exporter**
   - Image: `prom/node-exporter:latest`
   - Exposed on port 35763
   - Collects system metrics from the host

3. **cAdvisor**
   - Image: `gcr.io/cadvisor/cadvisor:latest`
   - Exposed on port 35764
   - Collects container metrics

4. **Grafana**
   - Image: `grafana/grafana:latest`
   - Exposed on port 35762
   - Dashboards and provisioning configurations mounted from local directories
   - Default admin credentials configured via environment variables
   - Persistent volume for data storage

5. **Nginx Exporter**
   - Image: `nginx/nginx-prometheus-exporter:latest`
   - Exposed on port 35765
   - Collects metrics from Nginx

6. **Redis Exporter**
   - Image: `oliver006/redis_exporter:latest`
   - Exposed on port 35766
   - Collects metrics from Redis

7. **Alertmanager**
   - Image: `prom/alertmanager:latest`
   - Exposed on port 35767
   - Configuration mounted from `./alertmanager/alertmanager.yml`

## Deployment Process

The deployment process is automated using GitHub Actions and is triggered by:
- Pushing to the `deploy` branch
- Manual trigger via workflow dispatch

### CI/CD Pipeline Steps

1. **Checkout Code**
   - Fetches the complete repository history

2. **Configure AWS Credentials**
   - Sets up AWS credentials for ECR access and EC2 deployment

3. **Login to Amazon ECR**
   - Authenticates with AWS ECR

4. **Build and Push Docker Image**
   - Builds the Docker image using the Dockerfile
   - Pushes the image to ECR with the `latest` tag

5. **Create Deployment Script**
   - Generates a deployment script (`deploy.sh`) that will:
     - Log into AWS ECR
     - Stop running containers
     - Remove untagged Docker images
     - Pull the latest image
     - Start the containers with Docker Compose

6. **Transfer Monitoring Stack**
   - Copies Grafana, Prometheus, and Alertmanager configurations to the EC2 instance

7. **Transfer Configuration Files**
   - Copies `nginx.conf`, `docker-compose.yml`, and `deploy.sh` to the EC2 instance

8. **Add Environment Variables**
   - Retrieves secrets from AWS Secrets Manager
   - Creates a `.env` file on the EC2 instance

9. **Run Docker Compose**
   - Executes the deployment script on the EC2 instance via AWS SSM
   - Waits for 60 seconds to allow services to start

10. **Send Slack Notification**
    - Notifies the team about the deployment status via Slack

## Configuration Details

### Docker Compose Configuration

The `docker-compose.yml` file defines all services, networks, and volumes:

- **Networks**:
  - `app-network`: Used by application components
  - `monitoring-network`: Used by monitoring components

- **Volumes**:
  - `prometheus_data`: Persistent storage for Prometheus
  - `grafana_data`: Persistent storage for Grafana
  - `/var/www/uploads`: Host directory mounted to container for file uploads

### Nginx Configuration

The Nginx configuration (`nginx.conf`) includes:

- Load balancing for the application server
- Proxy settings for forwarding requests
- Extended timeout settings for long-running requests
- Status endpoint for monitoring
- Maximum upload size set to 10MB

### Environment Variables

The application uses the following environment variables (stored in `.env`):

- **API Keys**:
  - `LLAMA_CLOUD_API_KEY`: For LlamaIndex API access
  - `OPENAI_API_KEY`: For OpenAI API access
  - `CLAUDE_API_KEY`: For Anthropic Claude API access
  - `NOMIC_API_KEY`: For Nomic API access
  - `GROQ_API_KEY`: For Groq API access

- **Langfuse Configuration**:
  - `LANGFUSE_SECRET_KEY`: For Langfuse authentication
  - `LANGFUSE_PUBLIC_KEY`: For Langfuse authentication
  - `LANGFUSE_HOST`: Langfuse service URL

- **Redis Configuration**:
  - `REDIS_HOST`: Redis server hostname
  - `REDIS_PORT`: Redis server port
  - `REDIS_DB`: Redis database number

- **Slack Integration**:
  - `SLACK_TOKEN`: For Slack notifications

## Access and Endpoints

The application is accessible at:
- URL: `http://ec2-3-75-220-58.eu-central-1.compute.amazonaws.com/`

Main API endpoints:
- `/`: Root endpoint (health check)
- `/ocr/*`: OCR processing endpoints
- Various other endpoints for Redis management and system checks

## Monitoring and Logging

### Logging

- All container logs are sent to AWS CloudWatch
- Log groups are organized by service:
  - `/aws/mediboard/ocr/service/logs`
  - `/aws/mediboard/ocr/redis/logs`
  - `/aws/mediboard/ocr/nginx/logs`

### Monitoring

- Prometheus collects metrics from all services
- Grafana provides visualization through dashboards
- Alertmanager handles alerting based on defined rules
- Exporters collect metrics from:
  - System (Node Exporter)
  - Containers (cAdvisor)
  - Nginx (Nginx Exporter)
  - Redis (Redis Exporter)

## Security Considerations

- API keys and secrets are stored in AWS Secrets Manager
- Secrets are injected into the environment at deployment time
- EC2 instance is accessed via SSH key for deployments
- AWS SSM is used for remote command execution

## Maintenance Procedures

### Deploying Updates

1. Push changes to the `deploy` branch or manually trigger the GitHub Actions workflow
2. The CI/CD pipeline will automatically:
   - Build and push a new Docker image
   - Deploy the updated image to the EC2 instance
   - Restart the services

### Scaling

The current deployment is configured for vertical scaling with significant resources allocated to the application container:
- CPU limits: 14 cores
- Memory limits: 32GB

For horizontal scaling, the configuration would need to be modified to:
- Add multiple application instances
- Configure Nginx for load balancing across instances
- Potentially move Redis to a managed service

### Backup and Recovery

- Persistent volumes are used for Prometheus and Grafana data
- Application data is stored in the `/var/www/uploads` directory on the host
- For disaster recovery, regular backups of these directories should be implemented

## Troubleshooting

### Common Issues

1. **Container Startup Failures**
   - Check container logs in AWS CloudWatch
   - Verify environment variables in the `.env` file
   - Check resource constraints

2. **API Connection Issues**
   - Verify API keys in the environment variables
   - Check network connectivity to external services
   - Review request/response logs

3. **Performance Issues**
   - Monitor resource usage through Grafana dashboards
   - Check for memory leaks or high CPU usage
   - Review container resource limits

### Accessing Logs

- AWS CloudWatch console: Navigate to the appropriate log group
- Docker logs: SSH into the EC2 instance and run `docker logs [container_name]`

### Monitoring Dashboards

- Grafana: Access via port 35762
- Prometheus: Access via port 35761

## Conclusion

The Mediboard-LLM system is deployed as a containerized application on AWS infrastructure with comprehensive monitoring and logging. The deployment is automated through a GitHub Actions CI/CD pipeline, making updates straightforward and reliable. The system is designed for high performance with significant resources allocated to the application container.
