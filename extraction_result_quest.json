{"patient_info": {"first_name": "SHMUEL", "last_name": "MEITAR"}, "physician_info": {"first_name": "ABRAHAM", "last_name": "MALKIN"}, "medical_facility": {"facility_name": "QUEST DIAGNOSTICS CLIFTON", "location": "1 INSIGHTS DRIVE, CLIFTON, NJ 07012-2355"}, "is_lab_report": true, "test_date": "11/17/2024", "lab_reports": [{"name": "DIRECT LDL", "result": 22, "range": "<100", "units": "mg/dL", "test_type": null, "comment": "Greatly elevated Triglycerides values (>1200 mg/dL) interfere with the dLDL assay. Desirable range <100 mg/dL for primary prevention; <70 mg/dL for patients with CHD or diabetic patients with > or = 2 CHD risk factors.", "comment_english": null, "index": 1, "result_value_type": "numeric_value"}, {"name": "SPECIMEN INTEGRITY COMPROMISED", "result": null, "range": null, "units": null, "test_type": null, "comment": "Whole blood, unspun or partially spun gel barrier tube was received more than 6 hours since collection. A false elevation of K, Phos and LD as well as a false decrease in glucose may occur due to prolonged contact with red cells.", "comment_english": null, "index": 2, "result_value_type": "blank"}, {"name": "GLUCOSE", "result": 104, "range": "65-99", "units": "mg/dL", "test_type": "COMPREHENSIVE METABOLIC PANEL", "comment": "For someone without known diabetes, a glucose value between 100 and 125 mg/dL is consistent with prediabetes and should be confirmed with a follow-up test.", "comment_english": null, "index": 3, "result_value_type": "numeric_value"}, {"name": "UREA NITROGEN (BUN)", "result": 21, "range": "7-25", "units": "mg/dL", "test_type": "COMPREHENSIVE METABOLIC PANEL", "comment": null, "comment_english": null, "index": 4, "result_value_type": "numeric_value"}, {"name": "CREATININE", "result": 1.04, "range": "0.70-1.22", "units": "mg/dL", "test_type": "COMPREHENSIVE METABOLIC PANEL", "comment": null, "comment_english": null, "index": 5, "result_value_type": "numeric_value"}, {"name": "EGFR", "result": 72, "range": "> OR = 60", "units": "mL/min/1.73m2", "test_type": "COMPREHENSIVE METABOLIC PANEL", "comment": null, "comment_english": null, "index": 6, "result_value_type": "numeric_value"}, {"name": "BUN/CREATININE RATIO", "result": null, "range": "6-22", "units": "(calc)", "test_type": "COMPREHENSIVE METABOLIC PANEL", "comment": "Not Reported: BUN and Creatinine are within reference range.", "comment_english": null, "index": 7, "result_value_type": "blank"}, {"name": "SODIUM", "result": 137, "range": "135-146", "units": "mmol/L", "test_type": "COMPREHENSIVE METABOLIC PANEL", "comment": null, "comment_english": null, "index": 8, "result_value_type": "numeric_value"}, {"name": "CHLORIDE", "result": 103, "range": "98-110", "units": "mmol/L", "test_type": "COMPREHENSIVE METABOLIC PANEL", "comment": null, "comment_english": null, "index": 9, "result_value_type": "numeric_value"}, {"name": "CARBON DIOXIDE", "result": 28, "range": "20-32", "units": "mmol/L", "test_type": "COMPREHENSIVE METABOLIC PANEL", "comment": null, "comment_english": null, "index": 10, "result_value_type": "numeric_value"}, {"name": "CALCIUM", "result": 8.8, "range": "8.6-10.3", "units": "mg/dL", "test_type": "COMPREHENSIVE METABOLIC PANEL", "comment": null, "comment_english": null, "index": 11, "result_value_type": "numeric_value"}, {"name": "PROTEIN, TOTAL", "result": 6.6, "range": "6.1-8.1", "units": "g/dL", "test_type": "COMPREHENSIVE METABOLIC PANEL", "comment": null, "comment_english": null, "index": 12, "result_value_type": "numeric_value"}, {"name": "ALBUMIN", "result": 4.2, "range": "3.6-5.1", "units": "g/dL", "test_type": "COMPREHENSIVE METABOLIC PANEL", "comment": null, "comment_english": null, "index": 13, "result_value_type": "numeric_value"}, {"name": "GLOBULIN", "result": 2.4, "range": "1.9-3.7", "units": "g/dL (calc)", "test_type": "COMPREHENSIVE METABOLIC PANEL", "comment": null, "comment_english": null, "index": 14, "result_value_type": "numeric_value"}, {"name": "ALBUMIN/GLOBULIN RATIO", "result": 1.8, "range": "1.0-2.5", "units": "(calc)", "test_type": "COMPREHENSIVE METABOLIC PANEL", "comment": null, "comment_english": null, "index": 15, "result_value_type": "numeric_value"}, {"name": "BILIRUBIN, TOTAL", "result": 0.6, "range": "0.2-1.2", "units": "mg/dL", "test_type": "COMPREHENSIVE METABOLIC PANEL", "comment": null, "comment_english": null, "index": 16, "result_value_type": "numeric_value"}, {"name": "ALKALINE PHOSPHATASE", "result": 110, "range": "35-144", "units": "U/L", "test_type": "COMPREHENSIVE METABOLIC PANEL", "comment": null, "comment_english": null, "index": 17, "result_value_type": "numeric_value"}, {"name": "AST", "result": 18, "range": "10-35", "units": "U/L", "test_type": "COMPREHENSIVE METABOLIC PANEL", "comment": null, "comment_english": null, "index": 18, "result_value_type": "numeric_value"}, {"name": "ALT", "result": 13, "range": "9-46", "units": "U/L", "test_type": "COMPREHENSIVE METABOLIC PANEL", "comment": null, "comment_english": null, "index": 19, "result_value_type": "numeric_value"}, {"name": "PHOSPHATE (AS PHOSPHORUS)", "result": 4.0, "range": "2.1-4.3", "units": "mg/dL", "test_type": "COMPREHENSIVE METABOLIC PANEL", "comment": null, "comment_english": null, "index": 20, "result_value_type": "numeric_value"}, {"name": "BILIRUBIN, DIRECT", "result": 0.2, "range": "< OR = 0.2", "units": "mg/dL", "test_type": null, "comment": null, "comment_english": null, "index": 21, "result_value_type": "numeric_value"}, {"name": "LD", "result": 180, "range": "120-250", "units": "U/L", "test_type": null, "comment": null, "comment_english": null, "index": 22, "result_value_type": "numeric_value"}, {"name": "CREATINE KINASE, TOTAL", "result": 98, "range": "44-196", "units": "U/L", "test_type": null, "comment": null, "comment_english": null, "index": 23, "result_value_type": "numeric_value"}, {"name": "GGT", "result": 31, "range": "3-70", "units": "U/L", "test_type": null, "comment": null, "comment_english": null, "index": 24, "result_value_type": "numeric_value"}, {"name": "WHITE BLOOD CELL COUNT", "result": 8.0, "range": "3.8-10.8", "units": "Thousand/uL", "test_type": "CBC (INCLUDES DIFF/PLT)", "comment": null, "comment_english": null, "index": 25, "result_value_type": "numeric_value"}, {"name": "RED BLOOD CELL COUNT", "result": 4.43, "range": "4.20-5.80", "units": "Million/uL", "test_type": "CBC (INCLUDES DIFF/PLT)", "comment": null, "comment_english": null, "index": 26, "result_value_type": "numeric_value"}, {"name": "HEMOGLOBIN", "result": 12.8, "range": "13.2-17.1", "units": "g/dL", "test_type": "CBC (INCLUDES DIFF/PLT)", "comment": null, "comment_english": null, "index": 27, "result_value_type": "numeric_value"}, {"name": "HEMATOCRIT", "result": 39.9, "range": "38.5-50.0", "units": "%", "test_type": "CBC (INCLUDES DIFF/PLT)", "comment": null, "comment_english": null, "index": 28, "result_value_type": "numeric_value"}, {"name": "MCV", "result": 90.1, "range": "80.0-100.0", "units": "fL", "test_type": "CBC (INCLUDES DIFF/PLT)", "comment": null, "comment_english": null, "index": 29, "result_value_type": "numeric_value"}, {"name": "MCH", "result": 28.9, "range": "27.0-33.0", "units": "pg", "test_type": "CBC (INCLUDES DIFF/PLT)", "comment": null, "comment_english": null, "index": 30, "result_value_type": "numeric_value"}, {"name": "MCHC", "result": 32.1, "range": "32.0-36.0", "units": "g/dL", "test_type": "CBC (INCLUDES DIFF/PLT)", "comment": "For adults, a slight decrease in the calculated MCHC value (in the range of 30 to 32 g/dL) is most likely not clinically significant; however, it should be interpreted with caution in correlation with other red cell parameters and the patient's clinical condition.", "comment_english": null, "index": 31, "result_value_type": "numeric_value"}, {"name": "RDW", "result": 16.1, "range": "11.0-15.0", "units": "%", "test_type": "CBC (INCLUDES DIFF/PLT)", "comment": null, "comment_english": null, "index": 32, "result_value_type": "numeric_value"}, {"name": "PLATELET COUNT", "result": 288, "range": "140-400", "units": "Thousand/uL", "test_type": "CBC (INCLUDES DIFF/PLT)", "comment": null, "comment_english": null, "index": 33, "result_value_type": "numeric_value"}, {"name": "MPV", "result": 12.6, "range": "7.5-12.5", "units": "fL", "test_type": "CBC (INCLUDES DIFF/PLT)", "comment": null, "comment_english": null, "index": 34, "result_value_type": "numeric_value"}, {"name": "ABSOLUTE NEUTROPHILS", "result": 3696, "range": "1500-7800", "units": "cells/uL", "test_type": "CBC (INCLUDES DIFF/PLT)", "comment": null, "comment_english": null, "index": 35, "result_value_type": "numeric_value"}, {"name": "ABSOLUTE LYMPHOCYTES", "result": 3016, "range": "850-3900", "units": "cells/uL", "test_type": "CBC (INCLUDES DIFF/PLT)", "comment": null, "comment_english": null, "index": 36, "result_value_type": "numeric_value"}, {"name": "ABSOLUTE MONOCYTES", "result": 976, "range": "200-950", "units": "cells/uL", "test_type": "CBC (INCLUDES DIFF/PLT)", "comment": null, "comment_english": null, "index": 37, "result_value_type": "numeric_value"}, {"name": "ABSOLUTE EOSINOPHILS", "result": 200, "range": "15-500", "units": "cells/uL", "test_type": "CBC (INCLUDES DIFF/PLT)", "comment": null, "comment_english": null, "index": 38, "result_value_type": "numeric_value"}, {"name": "ABSOLUTE BASOPHILS", "result": 112, "range": "0-200", "units": "cells/uL", "test_type": "CBC (INCLUDES DIFF/PLT)", "comment": null, "comment_english": null, "index": 39, "result_value_type": "numeric_value"}, {"name": "NEUTROPHILS", "result": 46.2, "range": null, "units": "%", "test_type": "CBC (INCLUDES DIFF/PLT)", "comment": null, "comment_english": null, "index": 40, "result_value_type": "numeric_value"}, {"name": "LYMPHOCYTES", "result": 37.7, "range": null, "units": "%", "test_type": "CBC (INCLUDES DIFF/PLT)", "comment": null, "comment_english": null, "index": 41, "result_value_type": "numeric_value"}, {"name": "MONOCYTES", "result": 12.2, "range": null, "units": "%", "test_type": "CBC (INCLUDES DIFF/PLT)", "comment": null, "comment_english": null, "index": 42, "result_value_type": "numeric_value"}, {"name": "EOSINOPHILS", "result": 2.5, "range": null, "units": "%", "test_type": "CBC (INCLUDES DIFF/PLT)", "comment": null, "comment_english": null, "index": 43, "result_value_type": "numeric_value"}, {"name": "BASOPHILS", "result": 1.4, "range": null, "units": "%", "test_type": "CBC (INCLUDES DIFF/PLT)", "comment": null, "comment_english": null, "index": 44, "result_value_type": "numeric_value"}, {"name": "IRON, TOTAL", "result": 60, "range": "50-180", "units": "mcg/dL", "test_type": "IRON AND TOTAL IRON BINDING CAPACITY", "comment": null, "comment_english": null, "index": 45, "result_value_type": "numeric_value"}, {"name": "IRON BINDING CAPACITY", "result": 318, "range": "250-425", "units": "mcg/dL (calc)", "test_type": "IRON AND TOTAL IRON BINDING CAPACITY", "comment": null, "comment_english": null, "index": 46, "result_value_type": "numeric_value"}, {"name": "% SATURATION", "result": 19, "range": "20-48", "units": "% (calc)", "test_type": "IRON AND TOTAL IRON BINDING CAPACITY", "comment": null, "comment_english": null, "index": 47, "result_value_type": "numeric_value"}, {"name": "FERRITIN", "result": 80, "range": "24-380", "units": "ng/mL", "test_type": "IRON AND TOTAL IRON BINDING CAPACITY", "comment": null, "comment_english": null, "index": 48, "result_value_type": "numeric_value"}, {"name": "QUEST AD DETECT (R) APOE ISOFORM, PLASMA", "result": "E3/E3", "range": null, "units": null, "test_type": null, "comment": "E3/E3: This combination of isoforms is most common and suggests an average risk of Alzheimer's disease. Patient sex, environment, race, ethnicity, and presence of other risk alleles also contribute to the risk of AD associated with APOE genotype (1,2). Patients who do not carry the APOE4 gene are of average risk for amyloid related imaging abnormalities (ARIA) when receiving amyloid-modifying therapies (3) (1) <PERSON>eu et al. JAMA Neurol. 2017;74:1178-1189 (2) <PERSON><PERSON> et al. Nat Rev Neurol. 2023;19:261-277 (3) <PERSON> et al. J Prev Alz Dis. 2023;10:362-377", "comment_english": null, "index": 49, "result_value_type": "blank"}, {"name": "CHOLESTEROL, TOTAL", "result": 85, "range": "<200", "units": "mg/dL", "test_type": "LIPID PANEL", "comment": null, "comment_english": null, "index": 50, "result_value_type": "numeric_value"}, {"name": "HDL CHOLESTEROL", "result": 58, "range": ">=40", "units": "mg/dL", "test_type": "LIPID PANEL", "comment": null, "comment_english": null, "index": 51, "result_value_type": "numeric_value"}, {"name": "TRIGLYCERIDES", "result": 55, "range": "<150", "units": "mg/dL", "test_type": "LIPID PANEL", "comment": null, "comment_english": null, "index": 52, "result_value_type": "numeric_value"}, {"name": "LDL-CHOLESTEROL", "result": 13, "range": "<100", "units": "mg/dL (calc)", "test_type": "LIPID PANEL", "comment": "Desirable range <100 mg/dL for primary prevention; <70 mg/dL for patients with CHD or diabetic patients with >= 2 CHD risk factors. LDL-C is now calculated using the Martin-Hopkins calculation, which is a validated novel method providing better accuracy than the <PERSON><PERSON><PERSON>ald equation in the estimation of LDL-<PERSON><PERSON> et al. JAMA. 2013;310(19): 2061-2068", "comment_english": null, "index": 53, "result_value_type": "numeric_value"}, {"name": "CHOL/HDLC RATIO", "result": 1.5, "range": "<=3.5", "units": "calc", "test_type": "LIPID PANEL", "comment": null, "comment_english": null, "index": 54, "result_value_type": "numeric_value"}, {"name": "NON-HDL CHOLESTEROL", "result": 27, "range": "<130", "units": "mg/dL (calc)", "test_type": "LIPID PANEL", "comment": "For patients with diabetes plus 1 major ASCVD risk factor, treating to a non-HDL-C goal of <100 mg/dL (LDL-C of <70 mg/dL) is considered a therapeutic option.", "comment_english": null, "index": 55, "result_value_type": "numeric_value"}, {"name": "LDL PARTICLE NUMBER", "result": 520, "range": "<1138", "units": "nmol/L", "test_type": "LIPOPROTEIN FRACTIONATION, ION MOBILIT", "comment": null, "comment_english": null, "index": 56, "result_value_type": "numeric_value"}, {"name": "LDL SMALL", "result": 83, "range": "<142", "units": "nmol/L", "test_type": "LIPOPROTEIN FRACTIONATION, ION MOBILIT", "comment": null, "comment_english": null, "index": 57, "result_value_type": "numeric_value"}, {"name": "LDL MEDIUM", "result": 88, "range": "<215", "units": "nmol/L", "test_type": "LIPOPROTEIN FRACTIONATION, ION MOBILIT", "comment": null, "comment_english": null, "index": 58, "result_value_type": "numeric_value"}, {"name": "HDL LARGE", "result": 5814, "range": ">6729", "units": "nmol/L", "test_type": "LIPOPROTEIN FRACTIONATION, ION MOBILIT", "comment": null, "comment_english": null, "index": 59, "result_value_type": "numeric_value"}, {"name": "LDL PATTERN", "result": "A", "range": "A", "units": "Pattern", "test_type": "LIPOPROTEIN FRACTIONATION, ION MOBILIT", "comment": null, "comment_english": null, "index": 60, "result_value_type": "blank"}, {"name": "LDL PEAK SIZE", "result": 218.7, "range": ">222.9", "units": "<PERSON><PERSON>", "test_type": "LIPOPROTEIN FRACTIONATION, ION MOBILIT", "comment": null, "comment_english": null, "index": 61, "result_value_type": "numeric_value"}, {"name": "APOLIPOPROTEIN B", "result": 27, "range": "<90", "units": "mg/dL", "test_type": "APOLIPOPROTEINS", "comment": "Risk: Optimal <90 mg/dL; Moderate 90-119 mg/dL; High >= 120 mg/dL; Cardiovascular event risk category cut points (optimal, moderate, high) are based on National Lipid Association recommendations- <PERSON><PERSON> TA et al. J of Clin Lipid. 2015; 9: 129-169 and <PERSON><PERSON><PERSON> et al. Endocr Pract. 2017;23(Suppl 2):1-87.", "comment_english": null, "index": 62, "result_value_type": "numeric_value"}, {"name": "LIPOPROTEIN (a)", "result": "<10", "range": "<75", "units": "nmol/L", "test_type": "APOLIPOPROTEINS", "comment": "Verified by repeat analysis. Risk: Optimal <75 nmol/L; Moderate 75-125 nmol/L; High >125 nmol/L. Cardiovascular event risk category cut points (optimal, moderate, high) are based on Tsimika S. JACC 2017;69:692-711.", "comment_english": null, "index": 63, "result_value_type": "operator_value"}]}